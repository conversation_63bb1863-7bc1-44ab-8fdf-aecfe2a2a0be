import React, { Component, Suspense, lazy } from 'react';
import { connect } from 'react-redux';
import { isPlainObject, isEqual, isNil, omitBy } from 'lodash';
import { cloud, cloudHook, CloudCtx } from '@youzan/ranta-cloud-react';
import {
  Pagination,
  Pop,
  Icon,
  Notify,
  Button,
  BlockLoading,
  Checkbox,
  ICheckboxProps,
} from 'zent';
import { Actions, Effect } from '../effects';
import Filter from '../components/filter';
import SubFilter from '../components/sub-filter/SubFilter';
import OrderList from '../components/order-list';
import { Announcement, SatisfactionDialog, AppCardList } from '@youzan/react-components';
import '@youzan/payback-ads/lib/index.css';
import { IS_WHOLESALE } from '../constants';
import api from '../api';
import { useSmartPreload } from '../components/lazy-components';

import {
  compatibleBackboneRouter,
  compatibleStarOrder,
  renderPageHelp,
  getFilterOptionsByQuery,
  insertPromoterInfoByQuery,
} from '../utils';
import { isOfflineAdmin, isQttShop, storeId as globalStoreId } from 'constants/role';
import { TIME_SORT_DEFAULT_VALUE } from '../constants';
import {
  IFormattedOrderListItem,
  IOrderListRequest,
  IOrderSearchQuery,
  IStoreListItem,
} from '../types';
import { IGetTipsResponse } from 'definitions/order/list';
import { IOrderListState } from '../states';
import RemarkBtn from 'pages/order/components/remark';
import { putBatchRemark } from 'pages/order/components/remark/api';
import { isPureWscSingleStore } from 'shared/fns/wsc-chain';
import getEvent from 'fns/event';
import { BatchPrintTaskProgressDialogCloseEvent } from '@youzan/order-domain-pc-components/es/batch-express-delivery/batch-print-dialog';
import { Position } from '@youzan/react-components/typings/components/app-card-list/types';
import { Loader } from '@youzan/content-loader-react';
import {
  OrderManageListBeforeFilter,
  Order,
  OrderManageListBeforeFilterReset,
} from '@youzan-cloud/cloud-biz-types';

// 懒加载组件
const LazyWholesaleAppBoard = lazy(() => import('components/wholesale-app-board'));
const LazyExpressAdBanner = lazy(() => import('components/electron-way-bill/express-ad-banner'));
const LazyBatchDeliveryPrintButton = lazy(() => import('../components/batch-delivery-print-button'));

// 上报用户行为接口
const { reportActions } = SatisfactionDialog;
const RULE_NAME = 'wsc_pc_trade_order_list_new'; // Apollo: wsc-survey

const PAGE_SIZE_OPTIONS = [20, 30, 40, 50];
let PAGE_SIZE_OPTIONS_MUTABLE = PAGE_SIZE_OPTIONS;
const MAX_TOTAL_PAGE = 100;

// 是否显示多网点
// @ts-ignore
const SHOW_MULTI_STORE = +window._global.isShowMultiStore === 1;
const SHOW_MULTI_STORE_STR = 'multistoreinfoonly';

interface IProps extends ROUTER<{}> {
  storeList: IStoreListItem[];
  orderList: IFormattedOrderListItem[];
  selectedItem: IFormattedOrderListItem[];
  orderTotal: number;
}

const SEARCH_METHOD = {
  nothing: 'nothing',
  manual: 'manual',
  orderOftenSearchConfig: 'orderOftenSearchConfig',
};
type SearchMethod = (typeof SEARCH_METHOD)[keyof typeof SEARCH_METHOD];

interface IState {
  filterOptions: IOrderSearchQuery;
  listLoading: boolean;
  tips: IGetTipsResponse;
  current?: number;
  salesmanAlert?: boolean;
  timeSortValue: string;
  addOrderOftenSearchConfig: {
    loading: boolean;
  };
  searchMethod: SearchMethod;
  showResultInfo: boolean;
  cacheFilterOptions: IOrderSearchQuery;
  isClear: boolean;
}

let cityOrderTimer: number;

@cloud()
class List extends Component<IProps, IState> {
  filterRef = React.createRef<any>();
  ctx!: CloudCtx<List>;

  /**
   * beforeFilter
   * @desc 订单筛选前触发
   */
  @cloud('beforeFilter', 'hook', { allowMultiple: true })
  beforeFilter = cloudHook<OrderManageListBeforeFilter>();

  /**
   * beforeFilterReset
   * @desc 订单筛选重置前触发
   */
  @cloud('beforeFilterReset', 'hook', { allowMultiple: true })
  beforeFilterReset = cloudHook<OrderManageListBeforeFilterReset>();

  /**
   * orderManageList
   * @desc 订单列表数据
   */
  @cloud('orderManageList', 'data')
  orderManageList: Order[];

  constructor(props) {
    super(props);

    if (IS_WHOLESALE) {
      props.location.query.type = 'wholesale';
    }
    this.state = {
      filterOptions: getFilterOptionsByQuery(props.location.query),
      cacheFilterOptions: getFilterOptionsByQuery(props.location.query),
      listLoading: false,
      tips: {} as IGetTipsResponse,
      salesmanAlert: false,
      timeSortValue: TIME_SORT_DEFAULT_VALUE,
      addOrderOftenSearchConfig: {
        loading: false,
      },
      searchMethod: SEARCH_METHOD.nothing,
      showResultInfo: false,
      isClear: false,
    };
    this.fetchTips();
  }

  componentDidMount() {
    // 满意度组件行为记录
    isPureWscSingleStore && reportActions({ rule: RULE_NAME });
    if (SHOW_MULTI_STORE) {
      Effect.app.getStoreList({ isOnline: 1 });
    }
    renderPageHelp(this.props.location.query);
    /** 兼容 bacnbone router 代码，有些老的 url  start */
    compatibleStarOrder(this.props.location.query);
    if (compatibleBackboneRouter(this.props.router)) {
      return;
    }
    /** 兼容 bacnbone router 代码，有些老的 url  end */
    // 如果是分销供货页添加query并拦截本次getList请求
    if (this.enterFx(this.props)) {
      return;
    }
    if (this.props.location.query.marketing_channel === 'DIRECT_SELLER') {
      this.setState({ salesmanAlert: true });
    }
    this.initPage();
    this.installCityOrder();

    getEvent().on(BatchPrintTaskProgressDialogCloseEvent, this.handleRefresh);
    // 性能埋点
    window.mark?.log();
  }

  componentWillUnmount = () => {
    this.uninstallCityOrder();
    getEvent().off(BatchPrintTaskProgressDialogCloseEvent, this.handleRefresh);
  };

  componentWillReceiveProps(nextProps) {
    /** 兼容 bacnbone router 代码，有些老的 url  start */
    compatibleStarOrder(nextProps.location.query);
    if (compatibleBackboneRouter(nextProps.router)) {
      return;
    }
    /** 兼容 bacnbone router 代码，有些老的 url  end */
    // 如果是分销供货页添加query并拦截本次getList请求
    if (this.enterFx(nextProps)) {
      return;
    }

    const pathnameHasChanged = this.props.location.pathname !== nextProps.location.pathname;
    const shouldGetList =
      !this.state.listLoading &&
      (pathnameHasChanged || !isEqual(this.props.location.query, nextProps.location.query));

    if (this.state.isClear) {
      return;
    }

    if (shouldGetList) {
      renderPageHelp(nextProps.location.query);
      this.getList(nextProps.location.query);
    }
  }

  componentDidUpdate(prevProps: IProps) {
    // 监听orderList的变化
    if (!isEqual(prevProps.orderList, this.props.orderList)) {
      // 将IFormattedOrderListItem[]转换为Order[]
      this.orderManageList = this.props.orderList.map((item) => {
        const { orderNo, tcExtra } = item;
        // @ts-ignore
        const { custom_tags: customTags } = tcExtra || {};
        return {
          orderNo,
          customTags,
        };
      }) as unknown as Order[];
    }
  }

  handleRefresh = () => {
    // @ts-ignore
    this.getList(this.props.location.query);
  };

  // 默认参数增加排序并更新路由
  initPage() {
    const [orderby, order] = TIME_SORT_DEFAULT_VALUE.split('/');
    // 如果当前参数有排序字段，覆盖默认排序字段
    const query = {
      orderby,
      order,
      ...this.props.location.query,
    };
    this.setState({
      timeSortValue: `${query.orderby}/${query.order}`,
      filterOptions: {
        ...this.state.filterOptions,
        order: query.order,
        orderby: query.orderby,
      },
    });
    // @ts-ignore
    this.getList(query);
    this.props.router.replace({
      pathname: this.props.location.pathname,
      query,
    });
  }

  fetchTips() {
    api.fetchDeliveryTips().then((data) => {
      this.setState({
        tips: data,
      });
    });
  }

  handleSearchMethodChange(searchMethod: SearchMethod) {
    this.setState({ searchMethod });
  }

  // 进入分销供货单时拼接type='fenxiao'
  // 用于解决 sam上菜单配置为/fx时，点击分销供货单显示的是所有订单 的问题
  enterFx = (props) => {
    const {
      location: { pathname, query },
      router: { replace },
    } = props;
    if (pathname === '/fx' && !query.type) {
      this.onFilterChange('type', 'fenxiao');
      replace({
        pathname,
        query: { ...query, type: 'fenxiao' },
      });
      return true;
    }
    return false;
  };

  onFilterChange = (name: IOrderSearchQuery | string, value: any, isRemoveState?: boolean) => {
    const { filterOptions } = this.state;
    let newFilterOptions = {
      ...filterOptions,
    };
    if (isPlainObject(name)) {
      newFilterOptions = {
        ...newFilterOptions,
        // @ts-ignore
        ...name,
      };
    } else {
      newFilterOptions = {
        ...newFilterOptions,
        // @ts-ignore
        [name]: value,
      };
      if (name === 'type') {
        newFilterOptions.state = 'all';
      }
      if (name === 'cashier_id' || name === 'seller_id') {
        // 添加字段用于展示在列表筛选页
        // @ts-ignore
        const key = name.match(/(.*)_(.*)/)[1];
        const ID = `${key}_id`;
        const NAME = `${key}_name`;
        if (value) {
          newFilterOptions[ID] = value.value;
          newFilterOptions[NAME] = value.text;
        } else {
          newFilterOptions[ID] = undefined;
          newFilterOptions[NAME] = undefined;
        }
      }
      if (name === 'sub_shop_kdt_id') {
        if (value) {
          const ID = 'sub_shop_kdt_id';
          const Name = 'sub_shop_name';
          newFilterOptions[ID] = value.value;
          newFilterOptions[Name] = value.text;
        }
      }
      if (name === 'state') {
        if (value === 'all') {
          newFilterOptions.states = ['all'];
        } else {
          if (isRemoveState || newFilterOptions.states.includes(value)) {
            newFilterOptions.states = newFilterOptions.states.filter((item) => item !== value);
          } else {
            newFilterOptions.states = [...newFilterOptions.states, value].filter(item => item !== 'all')
          }
        }

        if (!newFilterOptions.states.length) {
          newFilterOptions.states = ['all'];
        }
      }
    }
    this.setState({
      filterOptions: newFilterOptions,
    });
  };

  onFilterConfirm = async (filterOptions?: IOrderSearchQuery) => {
    if (!filterOptions) {
      filterOptions = this.state.filterOptions;
    }
    const {
      query: { page_size: pageSize = 20 },
    } = this.props.location;
    this.setState({
      salesmanAlert: filterOptions.marketing_channel === 'DIRECT_SELLER' ? true : false,
    });
    if (filterOptions.express_type !== 'city') {
      delete filterOptions.delivery_start_time;
      delete filterOptions.delivery_end_time;
    }

    try {
      const { cloud } = this.ctx;
      // @ts-ignore
      const cloudFilterFields: any = await cloud.invoke('beforeFilter');
      const customTags: Record<string, any> = {};
      // 先试后付的标签
      const trialTag: Record<string, any> = {};
      if (cloudFilterFields.length > 0) {
        cloudFilterFields.forEach((cloudFilterField) => {
          const cloudCustomTagsFilter = cloudFilterField?.customTags || {};
          const cloudTrialTagFilter = cloudFilterField?.trialTag || {};

          Object.keys(cloudCustomTagsFilter).forEach((cloudKey) => {
            const { list } = cloudCustomTagsFilter[cloudKey];
            // 目前只会拿取list的第一个值
            if (list && list.length > 0) {
              const [firstValue] = list; // 使用数组解构获取第一个值
              customTags[cloudKey] = firstValue.value;
            }
          });

          Object.keys(cloudTrialTagFilter).forEach((cloudKey) => {
            const { list } = cloudTrialTagFilter[cloudKey];
            // 目前只会拿取list的第一个值
            if (list && list.length > 0) {
              const [firstValue] = list; // 使用数组解构获取第一个值
              trialTag[cloudKey] = firstValue.value;
            }
          });
        });
      }

      if (Object.keys(customTags).length > 0) {
        filterOptions = {
          ...filterOptions,
          // @ts-ignore
          customTags: encodeURIComponent(JSON.stringify(customTags)),
        };
      }

      if (Object.keys(trialTag).length > 0) {
        filterOptions = {
          ...filterOptions,
          // @ts-ignore
          trialTag: encodeURIComponent(JSON.stringify(trialTag)),
        };
      }
    } catch (err) {
      console.log(err);
    }

    let newQuery: IOrderSearchQuery = {
      ...filterOptions,
      p: '1',
      // eslint-disable-next-line @typescript-eslint/camelcase
      // @ts-ignore
      page_size: pageSize,
    };

    // 剔除 null 或者 undefined 的值
    // @ts-ignore
    newQuery = omitBy(newQuery, isNil);

    // 附带一个：聚合搜索，和下面的理由同理
    // 特殊逻辑 --> JIRA(ONLINE-402469) 兼容后端性能问题 --> 针对搜索商品名称做处理
    // 商家每个订单拥有大量的 sku 数据，20条订单总数据量超过8M，超过了 dubbo 单个请求大小的限制。
    if (newQuery.aggregateSearchText) {
      // eslint-disable-next-line @typescript-eslint/camelcase
      newQuery.page_size = '10';
      PAGE_SIZE_OPTIONS_MUTABLE = [10];
    } else {
      // 之前置为15, 改正为默认的20
      if (newQuery.page_size === '10') {
        // eslint-disable-next-line @typescript-eslint/camelcase
        newQuery.page_size = 20;
      }
      // newQuery.page_size = 20;
      PAGE_SIZE_OPTIONS_MUTABLE = PAGE_SIZE_OPTIONS;
    }

    if (newQuery.state === 'delivery_over_time') {
      window.Logger &&
        window.Logger.log({
          et: 'click', // 事件类型
          ei: 'overdue_delivery_click', // 事件标识
          en: '超时未发货点击', // 事件名称
          pt: 'orderlist', // 页面类型
          params: {
            kdtid: window._global.kdtId,
          }, // 事件参数
        });
    }

    this.getList(newQuery);
    this.props.router.replace({
      pathname: this.props.location.pathname,
      query: newQuery,
    });
    this.setState({
      cacheFilterOptions: JSON.parse(JSON.stringify(filterOptions)),
      showResultInfo: true,
    });
  };

  handleClearFilterOptions = (defaultOptions = {} as IOrderSearchQuery) => {
    const filterOptions = getFilterOptionsByQuery(defaultOptions);
    this.setState(
      {
        filterOptions,
        isClear: true,
      },
      () => {
        this.handleSearchMethodChange('nothing');
        this.onFilterConfirm(filterOptions);
      }
    );
  };

  /**
   * 获取订单列表
   */
  getList = async (filterOptions: IOrderSearchQuery) => {
    this.setState({ listLoading: true });
    window.scrollTo(0, 0);
    const data = this.getFilterParams(filterOptions);
    await insertPromoterInfoByQuery(filterOptions, data);
    if (SHOW_MULTI_STORE) {
      // @ts-ignore
      data.extType = SHOW_MULTI_STORE_STR;
    }
    if (isQttShop) {
      // 群团团店铺需要调用专属接口，用字段做标记
      data.shopTypeName = 'qtt';
    }

    Effect.app.getOrderList(data).finally(() => {
      // 重新获取订单后要清空选中订单信息
      Actions.AppState.setPageItems(false);
      this.setState({ listLoading: false });
    });
  };

  getFilterParams(filterOptions: IOrderSearchQuery) {
    const data = {} as Required<IOrderListRequest>;
    data.keyword = {};
    const keys = Object.keys(filterOptions);
    const notKeywordKeysArr = [
      'type',
      'state',
      'states',
      'buy_way',
      'express_type',
      'feedback',
      'timeType',
      'order_label',
      'order_label_v2',
      'goods_title',
      'order',
      'p',
      'tuanId',
      'ext_type',
      'goods_id',
      'marketing_channel',
      'order_source',
      'buyer_id',
      'page_size',
      'orderby',
      'star',
      'seller_id',
      'cashier_id',
      'sub_shop_kdt_id',
      'eduSignUpStatus',
      'eduSignUpType', // 教育 - 报名类型
      'live_room_id',
      'excludeOrderSource',
      'promoterId',
      'timestamp',
      'participate_no',
      'qtt_note_title',
    ];
    
    keys.forEach((key) => {
      if (notKeywordKeysArr.indexOf(key) > -1 && filterOptions[key]) {
        data[key] = filterOptions[key];
      } else if (notKeywordKeysArr.indexOf(key) === -1 && filterOptions[key]) {
        data.keyword[key] = filterOptions[key];
      }
    });
    const labelKeys = [
      // 'order_no',
      'outer_transaction_number',
      // 'user_name',
      // 'tel',
      'expressNo',
      // 'tel_last4',
      // 'client_tel',
      // 'client_tel_last4',
      'seat_name',
      'promoter_name',
      'videoShopName',
    ];
    const keywordKeys = Object.keys(data.keyword);
    keywordKeys.forEach((key) => {
      if (labelKeys.indexOf(key) > -1 && filterOptions.order_label !== key) {
        delete data.keyword[key];
      }
    });

    /**
     * 网店管理员的 store_id 参数传 window._global.store_id
     * 网点管理员只能查询和导出自己网点的订单
     */
    if (isOfflineAdmin) {
      // eslint-disable-next-line @typescript-eslint/camelcase
      data.keyword.store_id = globalStoreId || 0;
    }

    const isCityOrder = filterOptions.express_type === 'city';
    const isSchedule = filterOptions.state === 'schedule';

    // (同城配送 && 定时达) 才带上配送时间参数，其他情况删掉这两个参数
    if (!isCityOrder || !isSchedule) {
      delete data.keyword.delivery_start_time;
      delete data.keyword.delivery_end_time;
    }

    // 超过MAX_TOTAL_PAGE页数，取第MAX_TOTAL_PAGE页数据
    // @ts-ignore
    if (data.p > MAX_TOTAL_PAGE) {
      data.p = MAX_TOTAL_PAGE;
      const { query = {}, pathname = '/' } = this.props.location;
      this.props.router.replace({ pathname, query: { ...query, p: MAX_TOTAL_PAGE } });
      Notify.error(`数据最多支持查看${MAX_TOTAL_PAGE}页`);
    }
    // 批发场景需要更换 type
    if (IS_WHOLESALE) {
      data.type = 'wholesale_order';
    }

    if (data.states && !(data.states instanceof Array)) {
      data.states = [data.states];
    }

    if (data.states && data.states.length) {
      delete data.state
    }

    if (this.state.filterOptions?.sellerRemark) {
      data.sellerRemark = this.state.filterOptions.sellerRemark;
    }
    if (this.state.filterOptions?.buyerMemo) {
      data.buyerMemo = this.state.filterOptions.buyerMemo;
    }
    if (this.state.filterOptions?.star) {
      const star = this.state.filterOptions?.star;
      data.star = '1';
      data.starNum = star ? star : '';
    }

    // 外部订单号取值逻辑调整
    if (data.keyword.outer_transaction_number) {
      data.keyword.real_outer_transaction_no = data.keyword.outer_transaction_number;
    }

    return data;
  }

  onPageChange = ({ current, pageSize }) => {
    const { query = {}, pathname = '/' } = this.props.location;
    this.props.router.replace({
      pathname,
      query: {
        ...query,
        // eslint-disable-next-line @typescript-eslint/camelcase
        page_size: pageSize,
        p: current,
      },
    });
    Actions.AppState.setPageItems(false);
  };

  handleAddOrderOftenSearchConfig = () => {
    const { cacheFilterOptions: filterOptions } = this.state;
    if (this.state.addOrderOftenSearchConfig.loading) return;
    this.setState({ addOrderOftenSearchConfig: { loading: true } });
    this.filterRef?.current?.wrappedForm
      ?.handleAddOrderOftenSearchConfig(filterOptions)
      .then(() => {
        this.setState({ addOrderOftenSearchConfig: { loading: false } });
      })
      .catch((e) => {
        console.log(e);
        this.setState({ addOrderOftenSearchConfig: { loading: false } });
      });
  };

  // 初始化同城送相关配置
  installCityOrder() {
    const { filterOptions } = this.state;
    // eslint-disable-next-line @youzan/wsc-pc/no-interval
    cityOrderTimer = window.setInterval(() => {
      Effect.app.getNoticeAndPlayAudio({
        cityOrderTimer,
        storeId: filterOptions.store_id,
      });
    }, 3000);
  }

  // 卸载同城送相关配置（释放定时器）
  uninstallCityOrder() {
    window.clearTimeout(cityOrderTimer);
  }

  getCheckStatus = (): Partial<ICheckboxProps<any>> => {
    const { selectedItem, orderList } = this.props;
    const checkStatus: ICheckboxProps<any> = {};
    // 数据请求中
    if (!orderList.length) {
      return { checked: false };
    }
    // 全选
    if (selectedItem.length === orderList.length) {
      checkStatus.checked = true;
      checkStatus.indeterminate = false;
    }
    // 选中部分 || 未选中
    if (selectedItem.length < orderList.length) {
      // 未选中
      if (selectedItem.length === 0) {
        checkStatus.checked = false;
        checkStatus.indeterminate = false;
      } else {
        checkStatus.indeterminate = true;
      }
    }
    return checkStatus;
  };

  handleBeforeFilterReset = () => {
    const { cloud } = this.ctx;
    return new Promise((resolve, reject) => {
      cloud.invoke('beforeFilterReset').then(resolve).catch(reject);
    });
  };

  renderBatchDeliveryPrintBtn = () => {
    const { filterOptions } = this.state;
    const { states } = filterOptions;
    // 待发货 | 全部 状态下展示
    if (states.includes('tosend') || states.includes('all')) {
      return (
        <Suspense fallback={<Button loading>批量发货</Button>}>
          <LazyBatchDeliveryPrintButton selectedItem={this.props.selectedItem} />
        </Suspense>
      );
    }

    return null
  };

  renderBatchRemark = () => {
    const checkStatus = this.getCheckStatus();
    return (
      <div className="batch-remark">
        <Checkbox
          {...checkStatus}
          onChange={(e) => Actions.AppState.setPageItems(e.target.checked)}
        >
          当页全选&nbsp;&nbsp;已选中{this.props.selectedItem.length}项
        </Checkbox>
        <RemarkBtn
          isBatch
          shouldOpenDialog={() => {
            if (!this.props.selectedItem.length) {
              Notify.warn('请先选择订单');
              return false;
            }
            return true;
          }}
          onConfirm={(remark) =>
            putBatchRemark({
              remark,
              orderNos: this.props.selectedItem.map((i) => i.orderNo),
            })
              .then(() => {
                // 清空之前批量备注选中
                Actions.AppState.setPageItems(false);
                Notify.success('批量备注成功');
              })
              .catch((msg) => Notify.error(msg || '批量备注失败'))
          }
          // @ts-ignore
          refresh={() => this.getList(this.props.location.query)}
          btn={<Button>批量备注</Button>}
        />
        {this.renderBatchDeliveryPrintBtn()}
      </div>
    );
  };

  render() {
    const { filterOptions, addOrderOftenSearchConfig } = this.state;
    const { storeList } = this.props;
    const {
      query: { p = 1, express_type: expressType, page_size: pageSize = 20, from_page: fromPage },
    }: any = this.props.location;
    return (
      <div>
        <div className="filter-panel">
          <Suspense fallback={<div style={{ height: '40px' }} />}>
            <LazyWholesaleAppBoard />
          </Suspense>
          <Suspense fallback={<div style={{ height: '60px' }} />}>
            <LazyExpressAdBanner />
          </Suspense>
          <Announcement url={'/v4/trade/announcement/apollo'} name="order-list" type="warning" />
          <Filter
            ref={this.filterRef}
            onFilterChange={this.onFilterChange}
            filterOptions={filterOptions}
            showMultiStore={SHOW_MULTI_STORE}
            storeList={storeList}
            onFilterConfirm={this.onFilterConfirm}
            loading={this.state.listLoading}
            getFilterParams={(filterOptions: IOrderSearchQuery) =>
              this.getFilterParams(filterOptions)
            }
            handleClearFilterOptions={this.handleClearFilterOptions}
            handleSearchMethodChange={(searchMethod: SearchMethod) =>
              this.handleSearchMethodChange(searchMethod)
            }
            onBeforeFilterReset={this.handleBeforeFilterReset}
          />
        </div>
        <div className="result-panel-wrap">
          {this.state.showResultInfo && !this.state.listLoading && (
            <div className="result-info">
              共筛选出 {this.props.orderTotal} 个订单
              <BlockLoading
                loading={addOrderOftenSearchConfig.loading}
                icon="circle"
                textPosition="right"
                iconSize={12}
              >
                {this.state.searchMethod === SEARCH_METHOD.manual && (
                  <Button
                    onClick={this.handleAddOrderOftenSearchConfig}
                    type="primary"
                    outline
                    size="small"
                  >
                    保存为常用筛选
                  </Button>
                )}
              </BlockLoading>
            </div>
          )}
          <div className="result-panel">
            <SubFilter
              filterOptions={filterOptions}
              onFilterConfirm={this.onFilterConfirm}
              onFilterChange={this.onFilterChange}
            />
            {this.renderBatchRemark()}
            {/* 同城配送订单需要根据 expressType 来查单个订单的所有信息 */}
            <OrderList
              {...this.props}
              filterOptions={this.state.filterOptions}
              list={this.props.orderList}
              selectedItems={this.props.selectedItem}
              loading={this.state.listLoading}
              current={this.state.current}
              onFilterChange={this.onFilterChange}
              onFilterConfirm={this.onFilterConfirm}
              expressType={expressType}
              tips={this.state.tips}
            />
            {+p >= MAX_TOTAL_PAGE && this.props.orderTotal > pageSize * MAX_TOTAL_PAGE ? (
              <div className="page-more-tip">
                订单最多支持查看{MAX_TOTAL_PAGE}页，请通过筛选或调整每页条数查看更多。
              </div>
            ) : null}
            <div className="order-list-pagination">
              {fromPage === 'goods' && (
                <Pop
                  trigger="hover"
                  content="此处订单数不等同于商品销量（商品销量=商品累计支付件数 - 商品累计全额退款件数）"
                  centerArrow
                  position="top-left"
                >
                  <Icon type="help-circle" className="order-help-circle" />
                </Pop>
              )}
              <Pagination
                current={Math.min(+p, MAX_TOTAL_PAGE)}
                total={this.props.orderTotal}
                pageSizeOptions={PAGE_SIZE_OPTIONS_MUTABLE}
                pageSize={pageSize}
                onChange={this.onPageChange}
              />
            </div>
          </div>
        </div>
        <div>
          {/* 手动分割线。 为了<AppCardList> 要始终置底展示*/}
          <div className="divider"></div>
          <AppCardList position={AppCardList.Position.OrderManage as Position} />
        </div>
        {/* https://qima.feishu.cn/docx/doxcnv6F7rfPjrd4eKs0ka2WJKg */}
        {isPureWscSingleStore && <SatisfactionDialog rule={RULE_NAME} />}
        <Loader url="/v4/assets/output" content="security/shop-open-gift" />
        {/* 开店礼包二期弹窗 */}
        <Loader
          url="/v4/assets/output"
          content="security/guarantee-subsidy-ad-dialog"
          props={{
            sceneNo: 'ORDER_LIST_POPUP',
          }}
        />
      </div>
    );
  }
}

export default connect((state: IOrderListState) => {
  return {
    storeList: state.app.storeList,
    orderList: state.app.orderList,
    selectedItem: state.app.selectedItem,
    orderTotal: state.app.orderTotal,
  };
})(List);
