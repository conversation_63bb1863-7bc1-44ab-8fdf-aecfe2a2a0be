import { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { debounce } from 'lodash';

interface FetchState<T> {
  data: T | null;
  loading: boolean;
  error: Error | null;
  lastFetch: number;
}

interface FetchOptions {
  debounceMs?: number;
  cacheTime?: number;
  retryCount?: number;
  retryDelay?: number;
}

/**
 * 优化的数据获取 Hook
 * 提供防抖、缓存、重试、取消请求等功能
 */
export function useOptimizedDataFetch<T, P = any>(
  fetchFunction: (params: P) => Promise<T>,
  initialParams?: P,
  options: FetchOptions = {}
) {
  const {
    debounceMs = 300,
    cacheTime = 5 * 60 * 1000, // 5分钟缓存
    retryCount = 3,
    retryDelay = 1000
  } = options;

  const [state, setState] = useState<FetchState<T>>({
    data: null,
    loading: false,
    error: null,
    lastFetch: 0
  });

  const abortControllerRef = useRef<AbortController | null>(null);
  const cacheRef = useRef<Map<string, { data: T; timestamp: number }>>(new Map());
  const retryTimeoutRef = useRef<NodeJS.Timeout>();

  // 生成缓存键
  const getCacheKey = useCallback((params: P) => {
    return JSON.stringify(params);
  }, []);

  // 检查缓存是否有效
  const isCacheValid = useCallback((timestamp: number) => {
    return Date.now() - timestamp < cacheTime;
  }, [cacheTime]);

  // 从缓存获取数据
  const getFromCache = useCallback((params: P) => {
    const key = getCacheKey(params);
    const cached = cacheRef.current.get(key);
    if (cached && isCacheValid(cached.timestamp)) {
      return cached.data;
    }
    return null;
  }, [getCacheKey, isCacheValid]);

  // 设置缓存
  const setCache = useCallback((params: P, data: T) => {
    const key = getCacheKey(params);
    cacheRef.current.set(key, {
      data,
      timestamp: Date.now()
    });
  }, [getCacheKey]);

  // 清理缓存
  const clearCache = useCallback(() => {
    cacheRef.current.clear();
  }, []);

  // 取消当前请求
  const cancelRequest = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
      retryTimeoutRef.current = undefined;
    }
  }, []);

  // 执行数据获取
  const fetchData = useCallback(async (params: P, attempt = 0): Promise<void> => {
    // 检查缓存
    const cachedData = getFromCache(params);
    if (cachedData) {
      setState(prev => ({
        ...prev,
        data: cachedData,
        loading: false,
        error: null
      }));
      return;
    }

    // 取消之前的请求
    cancelRequest();

    // 创建新的 AbortController
    abortControllerRef.current = new AbortController();

    setState(prev => ({
      ...prev,
      loading: true,
      error: null
    }));

    try {
      const data = await fetchFunction(params);
      
      // 检查请求是否被取消
      if (abortControllerRef.current?.signal.aborted) {
        return;
      }

      // 设置缓存
      setCache(params, data);

      setState({
        data,
        loading: false,
        error: null,
        lastFetch: Date.now()
      });
    } catch (error) {
      // 检查请求是否被取消
      if (abortControllerRef.current?.signal.aborted) {
        return;
      }

      const err = error as Error;
      
      // 重试逻辑
      if (attempt < retryCount && err.name !== 'AbortError') {
        retryTimeoutRef.current = setTimeout(() => {
          fetchData(params, attempt + 1);
        }, retryDelay * Math.pow(2, attempt)); // 指数退避
        return;
      }

      setState(prev => ({
        ...prev,
        loading: false,
        error: err
      }));
    }
  }, [fetchFunction, getFromCache, setCache, cancelRequest, retryCount, retryDelay]);

  // 防抖的数据获取函数
  const debouncedFetchData = useMemo(
    () => debounce(fetchData, debounceMs),
    [fetchData, debounceMs]
  );

  // 立即获取数据（不防抖）
  const fetchDataImmediate = useCallback((params: P) => {
    debouncedFetchData.cancel();
    fetchData(params);
  }, [fetchData, debouncedFetchData]);

  // 刷新数据（忽略缓存）
  const refreshData = useCallback((params: P) => {
    const key = getCacheKey(params);
    cacheRef.current.delete(key);
    fetchDataImmediate(params);
  }, [getCacheKey, fetchDataImmediate]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      cancelRequest();
      debouncedFetchData.cancel();
    };
  }, [cancelRequest, debouncedFetchData]);

  // 初始数据获取
  useEffect(() => {
    if (initialParams) {
      fetchData(initialParams);
    }
  }, []); // 只在组件挂载时执行

  return {
    ...state,
    fetchData: debouncedFetchData,
    fetchDataImmediate,
    refreshData,
    clearCache,
    cancelRequest,
    isStale: useMemo(() => {
      return state.lastFetch > 0 && Date.now() - state.lastFetch > cacheTime;
    }, [state.lastFetch, cacheTime])
  };
}

/**
 * 优化的分页数据获取 Hook
 */
export function useOptimizedPaginatedFetch<T, P = any>(
  fetchFunction: (params: P & { page: number; pageSize: number }) => Promise<{ list: T[]; total: number }>,
  initialParams?: P,
  options: FetchOptions & { initialPage?: number; initialPageSize?: number } = {}
) {
  const { initialPage = 1, initialPageSize = 20, ...fetchOptions } = options;
  
  const [pagination, setPagination] = useState({
    page: initialPage,
    pageSize: initialPageSize,
    total: 0
  });

  const fetchWithPagination = useCallback(
    (params: P & { page?: number; pageSize?: number }) => {
      const { page = pagination.page, pageSize = pagination.pageSize, ...otherParams } = params;
      return fetchFunction({ ...otherParams as P, page, pageSize });
    },
    [fetchFunction, pagination.page, pagination.pageSize]
  );

  const {
    data,
    loading,
    error,
    fetchData,
    fetchDataImmediate,
    refreshData,
    clearCache
  } = useOptimizedDataFetch(fetchWithPagination, initialParams, fetchOptions);

  // 更新分页信息
  useEffect(() => {
    if (data) {
      setPagination(prev => ({
        ...prev,
        total: data.total
      }));
    }
  }, [data]);

  const changePage = useCallback((page: number) => {
    setPagination(prev => ({ ...prev, page }));
    if (initialParams) {
      fetchDataImmediate({ ...initialParams, page, pageSize: pagination.pageSize });
    }
  }, [initialParams, fetchDataImmediate, pagination.pageSize]);

  const changePageSize = useCallback((pageSize: number) => {
    setPagination(prev => ({ ...prev, pageSize, page: 1 }));
    if (initialParams) {
      fetchDataImmediate({ ...initialParams, page: 1, pageSize });
    }
  }, [initialParams, fetchDataImmediate]);

  return {
    data: data?.list || [],
    total: data?.total || 0,
    loading,
    error,
    pagination,
    fetchData,
    fetchDataImmediate,
    refreshData,
    clearCache,
    changePage,
    changePageSize
  };
}
