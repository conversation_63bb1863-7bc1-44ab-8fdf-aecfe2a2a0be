import { useState, useCallback, useMemo, useRef } from 'react';
import { isEqual } from 'lodash';

/**
 * 优化的状态管理 Hook
 * 提供防抖更新、浅比较优化、批量更新等功能
 */
export function useOptimizedState<T>(initialState: T) {
  const [state, setState] = useState<T>(initialState);
  const stateRef = useRef<T>(state);
  const updateTimeoutRef = useRef<NodeJS.Timeout>();

  // 更新状态引用
  stateRef.current = state;

  // 浅比较更新状态
  const setStateWithShallowCompare = useCallback((newState: T | ((prevState: T) => T)) => {
    setState(prevState => {
      const nextState = typeof newState === 'function' 
        ? (newState as (prevState: T) => T)(prevState)
        : newState;
      
      // 浅比较，避免不必要的更新
      if (isEqual(prevState, nextState)) {
        return prevState;
      }
      
      return nextState;
    });
  }, []);

  // 防抖更新状态
  const setStateDebounced = useCallback((newState: T | ((prevState: T) => T), delay = 300) => {
    if (updateTimeoutRef.current) {
      clearTimeout(updateTimeoutRef.current);
    }
    
    updateTimeoutRef.current = setTimeout(() => {
      setStateWithShallowCompare(newState);
    }, delay);
  }, [setStateWithShallowCompare]);

  // 批量更新状态
  const batchUpdateState = useCallback((updates: Partial<T>) => {
    setStateWithShallowCompare(prevState => ({
      ...prevState,
      ...updates
    }));
  }, [setStateWithShallowCompare]);

  // 获取当前状态（不触发重渲染）
  const getCurrentState = useCallback(() => stateRef.current, []);

  return {
    state,
    setState: setStateWithShallowCompare,
    setStateDebounced,
    batchUpdateState,
    getCurrentState
  };
}

/**
 * 优化的列表状态管理 Hook
 * 专门用于处理大列表的状态管理
 */
export function useOptimizedListState<T>(initialList: T[] = []) {
  const [list, setList] = useState<T[]>(initialList);
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  
  // 使用 Map 缓存列表项，提高查找性能
  const listMap = useMemo(() => {
    const map = new Map<string, T>();
    list.forEach((item, index) => {
      // 假设每个项都有一个唯一的 id 或 key
      const key = (item as any).id || (item as any).key || String(index);
      map.set(key, item);
    });
    return map;
  }, [list]);

  // 优化的选择项管理
  const toggleSelection = useCallback((itemKey: string) => {
    setSelectedItems(prev => {
      const newSet = new Set(prev);
      if (newSet.has(itemKey)) {
        newSet.delete(itemKey);
      } else {
        newSet.add(itemKey);
      }
      return newSet;
    });
  }, []);

  const selectAll = useCallback(() => {
    const allKeys = Array.from(listMap.keys());
    setSelectedItems(new Set(allKeys));
  }, [listMap]);

  const clearSelection = useCallback(() => {
    setSelectedItems(new Set());
  }, []);

  const isSelected = useCallback((itemKey: string) => {
    return selectedItems.has(itemKey);
  }, [selectedItems]);

  // 批量操作
  const updateListItem = useCallback((itemKey: string, updates: Partial<T>) => {
    setList(prevList => 
      prevList.map(item => {
        const key = (item as any).id || (item as any).key;
        if (key === itemKey) {
          return { ...item, ...updates };
        }
        return item;
      })
    );
  }, []);

  const removeListItem = useCallback((itemKey: string) => {
    setList(prevList => 
      prevList.filter(item => {
        const key = (item as any).id || (item as any).key;
        return key !== itemKey;
      })
    );
    setSelectedItems(prev => {
      const newSet = new Set(prev);
      newSet.delete(itemKey);
      return newSet;
    });
  }, []);

  return {
    list,
    setList,
    listMap,
    selectedItems,
    selectedItemsArray: Array.from(selectedItems),
    toggleSelection,
    selectAll,
    clearSelection,
    isSelected,
    updateListItem,
    removeListItem
  };
}

/**
 * 优化的过滤器状态管理 Hook
 */
export function useOptimizedFilterState<T extends Record<string, any>>(initialFilters: T) {
  const { state: filters, setState: setFilters, setStateDebounced, batchUpdateState } = useOptimizedState(initialFilters);
  
  // 缓存过滤后的结果
  const filtersHash = useMemo(() => {
    return JSON.stringify(filters);
  }, [filters]);

  const updateFilter = useCallback((key: keyof T, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  }, [setFilters]);

  const updateFilterDebounced = useCallback((key: keyof T, value: any, delay = 300) => {
    setStateDebounced(prev => ({
      ...prev,
      [key]: value
    }), delay);
  }, [setStateDebounced]);

  const resetFilters = useCallback(() => {
    setFilters(initialFilters);
  }, [setFilters, initialFilters]);

  const hasActiveFilters = useMemo(() => {
    return !isEqual(filters, initialFilters);
  }, [filters, initialFilters]);

  return {
    filters,
    setFilters,
    updateFilter,
    updateFilterDebounced,
    batchUpdateFilters: batchUpdateState,
    resetFilters,
    hasActiveFilters,
    filtersHash
  };
}
