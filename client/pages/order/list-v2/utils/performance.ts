import { debounce, throttle } from 'lodash';

/**
 * 性能优化工具函数集合
 */

/**
 * 创建防抖函数，支持立即执行选项
 */
export function createDebounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  options: { leading?: boolean; trailing?: boolean } = {}
): T & { cancel: () => void; flush: () => ReturnType<T> } {
  return debounce(func, wait, options);
}

/**
 * 创建节流函数
 */
export function createThrottle<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  options: { leading?: boolean; trailing?: boolean } = {}
): T & { cancel: () => void; flush: () => ReturnType<T> } {
  return throttle(func, wait, options);
}

/**
 * 批量执行函数，在下一个事件循环中执行
 */
export function batchExecute(callback: () => void): void {
  if (typeof requestIdleCallback !== 'undefined') {
    requestIdleCallback(callback, { timeout: 100 });
  } else {
    setTimeout(callback, 0);
  }
}

/**
 * 延迟执行函数，直到浏览器空闲时
 */
export function executeOnIdle(callback: () => void, timeout = 5000): number {
  if (typeof requestIdleCallback !== 'undefined') {
    return requestIdleCallback(callback, { timeout });
  } else {
    return setTimeout(callback, 0) as any;
  }
}

/**
 * 取消空闲执行
 */
export function cancelIdleExecution(id: number): void {
  if (typeof cancelIdleCallback !== 'undefined') {
    cancelIdleCallback(id);
  } else {
    clearTimeout(id);
  }
}

/**
 * 内存优化的对象比较函数
 */
export function shallowEqual(obj1: any, obj2: any): boolean {
  if (obj1 === obj2) return true;
  
  if (obj1 == null || obj2 == null) return false;
  
  if (typeof obj1 !== 'object' || typeof obj2 !== 'object') return false;
  
  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);
  
  if (keys1.length !== keys2.length) return false;
  
  for (let key of keys1) {
    if (!keys2.includes(key) || obj1[key] !== obj2[key]) {
      return false;
    }
  }
  
  return true;
}

/**
 * 深度比较函数（性能优化版本）
 */
export function deepEqual(obj1: any, obj2: any, maxDepth = 10): boolean {
  if (maxDepth <= 0) return obj1 === obj2;
  
  if (obj1 === obj2) return true;
  
  if (obj1 == null || obj2 == null) return false;
  
  if (typeof obj1 !== typeof obj2) return false;
  
  if (typeof obj1 !== 'object') return obj1 === obj2;
  
  if (Array.isArray(obj1) !== Array.isArray(obj2)) return false;
  
  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);
  
  if (keys1.length !== keys2.length) return false;
  
  for (let key of keys1) {
    if (!keys2.includes(key)) return false;
    if (!deepEqual(obj1[key], obj2[key], maxDepth - 1)) return false;
  }
  
  return true;
}

/**
 * 创建缓存函数
 */
export function createCache<K, V>(maxSize = 100) {
  const cache = new Map<K, V>();
  
  return {
    get(key: K): V | undefined {
      const value = cache.get(key);
      if (value !== undefined) {
        // LRU: 重新设置以更新顺序
        cache.delete(key);
        cache.set(key, value);
      }
      return value;
    },
    
    set(key: K, value: V): void {
      if (cache.has(key)) {
        cache.delete(key);
      } else if (cache.size >= maxSize) {
        // 删除最旧的项
        const firstKey = cache.keys().next().value;
        cache.delete(firstKey);
      }
      cache.set(key, value);
    },
    
    has(key: K): boolean {
      return cache.has(key);
    },
    
    delete(key: K): boolean {
      return cache.delete(key);
    },
    
    clear(): void {
      cache.clear();
    },
    
    get size(): number {
      return cache.size;
    }
  };
}

/**
 * 创建带过期时间的缓存
 */
export function createTTLCache<K, V>(defaultTTL = 5 * 60 * 1000) {
  const cache = new Map<K, { value: V; expiry: number }>();
  
  const cleanup = () => {
    const now = Date.now();
    for (const [key, item] of cache.entries()) {
      if (now > item.expiry) {
        cache.delete(key);
      }
    }
  };
  
  // 定期清理过期项
  const cleanupInterval = setInterval(cleanup, defaultTTL);
  
  return {
    get(key: K): V | undefined {
      const item = cache.get(key);
      if (item) {
        if (Date.now() <= item.expiry) {
          return item.value;
        } else {
          cache.delete(key);
        }
      }
      return undefined;
    },
    
    set(key: K, value: V, ttl = defaultTTL): void {
      cache.set(key, {
        value,
        expiry: Date.now() + ttl
      });
    },
    
    has(key: K): boolean {
      const item = cache.get(key);
      if (item && Date.now() <= item.expiry) {
        return true;
      }
      if (item) {
        cache.delete(key);
      }
      return false;
    },
    
    delete(key: K): boolean {
      return cache.delete(key);
    },
    
    clear(): void {
      cache.clear();
    },
    
    cleanup,
    
    destroy(): void {
      clearInterval(cleanupInterval);
      cache.clear();
    },
    
    get size(): number {
      cleanup(); // 清理过期项后返回大小
      return cache.size;
    }
  };
}

/**
 * 性能监控工具
 */
export class PerformanceMonitor {
  private marks: Map<string, number> = new Map();
  private measures: Map<string, number> = new Map();
  
  mark(name: string): void {
    this.marks.set(name, performance.now());
  }
  
  measure(name: string, startMark: string, endMark?: string): number {
    const startTime = this.marks.get(startMark);
    if (startTime === undefined) {
      console.warn(`Start mark "${startMark}" not found`);
      return 0;
    }
    
    const endTime = endMark ? this.marks.get(endMark) : performance.now();
    if (endTime === undefined) {
      console.warn(`End mark "${endMark}" not found`);
      return 0;
    }
    
    const duration = endTime - startTime;
    this.measures.set(name, duration);
    return duration;
  }
  
  getMeasure(name: string): number | undefined {
    return this.measures.get(name);
  }
  
  getAllMeasures(): Record<string, number> {
    return Object.fromEntries(this.measures);
  }
  
  clear(): void {
    this.marks.clear();
    this.measures.clear();
  }
  
  log(name?: string): void {
    if (name) {
      const measure = this.measures.get(name);
      if (measure !== undefined) {
        console.log(`Performance [${name}]: ${measure.toFixed(2)}ms`);
      }
    } else {
      console.table(this.getAllMeasures());
    }
  }
}

// 全局性能监控实例
export const performanceMonitor = new PerformanceMonitor();

/**
 * 性能装饰器
 */
export function measurePerformance(name?: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    const measureName = name || `${target.constructor.name}.${propertyKey}`;
    
    descriptor.value = function (...args: any[]) {
      performanceMonitor.mark(`${measureName}-start`);
      const result = originalMethod.apply(this, args);
      
      if (result instanceof Promise) {
        return result.finally(() => {
          performanceMonitor.measure(measureName, `${measureName}-start`);
        });
      } else {
        performanceMonitor.measure(measureName, `${measureName}-start`);
        return result;
      }
    };
    
    return descriptor;
  };
}
