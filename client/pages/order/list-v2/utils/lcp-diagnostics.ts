/**
 * LCP (Largest Contentful Paint) 性能诊断工具
 * 用于分析和监控页面的 LCP 性能指标
 */

interface LCPEntry {
  element: Element;
  renderTime: number;
  loadTime: number;
  size: number;
  id: string;
  url?: string;
}

interface LCPDiagnostics {
  lcpValue: number;
  lcpElement: Element | null;
  lcpElementInfo: {
    tagName: string;
    id: string;
    className: string;
    size: number;
    isImage: boolean;
    imageUrl?: string;
    textContent?: string;
  } | null;
  renderBlockingResources: string[];
  criticalResourcesLoadTime: Record<string, number>;
  recommendations: string[];
}

class LCPAnalyzer {
  private lcpEntries: LCPEntry[] = [];
  private observer: PerformanceObserver | null = null;
  private resourceObserver: PerformanceObserver | null = null;
  private renderBlockingResources: string[] = [];
  private criticalResourcesLoadTime: Record<string, number> = {};

  constructor() {
    this.initLCPObserver();
    this.initResourceObserver();
    this.analyzeRenderBlockingResources();
  }

  private initLCPObserver() {
    if ('PerformanceObserver' in window) {
      this.observer = new PerformanceObserver((list) => {
        const entries = list.getEntries() as any[];
        entries.forEach((entry) => {
          if (entry.entryType === 'largest-contentful-paint') {
            this.lcpEntries.push({
              element: entry.element,
              renderTime: entry.renderTime,
              loadTime: entry.loadTime,
              size: entry.size,
              id: entry.id || '',
              url: entry.url
            });
          }
        });
      });

      try {
        this.observer.observe({ entryTypes: ['largest-contentful-paint'] });
      } catch (e) {
        console.warn('LCP observer not supported:', e);
      }
    }
  }

  private initResourceObserver() {
    if ('PerformanceObserver' in window) {
      this.resourceObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (entry.entryType === 'resource') {
            const resourceEntry = entry as PerformanceResourceTiming;
            this.criticalResourcesLoadTime[resourceEntry.name] = resourceEntry.loadEventEnd - resourceEntry.startTime;
          }
        });
      });

      try {
        this.resourceObserver.observe({ entryTypes: ['resource'] });
      } catch (e) {
        console.warn('Resource observer not supported:', e);
      }
    }
  }

  private analyzeRenderBlockingResources() {
    // 分析阻塞渲染的资源
    const stylesheets = document.querySelectorAll('link[rel="stylesheet"]');
    const scripts = document.querySelectorAll('script[src]:not([async]):not([defer])');

    stylesheets.forEach((link) => {
      const href = (link as HTMLLinkElement).href;
      if (href) {
        this.renderBlockingResources.push(href);
      }
    });

    scripts.forEach((script) => {
      const src = (script as HTMLScriptElement).src;
      if (src) {
        this.renderBlockingResources.push(src);
      }
    });
  }

  private getElementInfo(element: Element) {
    if (!element) return null;

    const isImage = element.tagName.toLowerCase() === 'img';
    const info = {
      tagName: element.tagName.toLowerCase(),
      id: element.id || '',
      className: element.className || '',
      size: 0,
      isImage,
      imageUrl: isImage ? (element as HTMLImageElement).src : undefined,
      textContent: !isImage ? element.textContent?.substring(0, 100) : undefined
    };

    // 计算元素大小
    const rect = element.getBoundingClientRect();
    info.size = rect.width * rect.height;

    return info;
  }

  private generateRecommendations(diagnostics: Partial<LCPDiagnostics>): string[] {
    const recommendations: string[] = [];

    if (diagnostics.lcpValue && diagnostics.lcpValue > 2500) {
      recommendations.push('LCP 超过 2.5 秒，需要优化');
    }

    if (diagnostics.lcpElementInfo?.isImage) {
      recommendations.push('LCP 元素是图片，建议优化图片加载：使用 WebP 格式、添加 preload、优化图片尺寸');
    }

    if (diagnostics.renderBlockingResources && diagnostics.renderBlockingResources.length > 5) {
      recommendations.push(`发现 ${diagnostics.renderBlockingResources.length} 个阻塞渲染的资源，建议减少或异步加载`);
    }

    // 检查关键资源加载时间
    const slowResources = Object.entries(diagnostics.criticalResourcesLoadTime || {})
      .filter(([_, time]) => time > 1000)
      .map(([url]) => url);

    if (slowResources.length > 0) {
      recommendations.push(`发现 ${slowResources.length} 个加载缓慢的资源，建议优化或使用 CDN`);
    }

    // 检查是否有内联样式
    const inlineStyles = document.querySelectorAll('style');
    if (inlineStyles.length > 3) {
      recommendations.push('发现过多内联样式，建议合并到外部 CSS 文件');
    }

    // 检查是否使用了字体
    const fontFaces = document.styleSheets;
    let hasFonts = false;
    try {
      Array.from(fontFaces).forEach(sheet => {
        if (sheet.cssRules) {
          Array.from(sheet.cssRules).forEach(rule => {
            if (rule.type === CSSRule.FONT_FACE_RULE) {
              hasFonts = true;
            }
          });
        }
      });
    } catch (e) {
      // 忽略跨域错误
    }

    if (hasFonts) {
      recommendations.push('检测到自定义字体，建议使用 font-display: swap 和字体预加载');
    }

    return recommendations;
  }

  public getDiagnostics(): LCPDiagnostics {
    const latestLCPEntry = this.lcpEntries[this.lcpEntries.length - 1];
    
    const diagnostics: Partial<LCPDiagnostics> = {
      lcpValue: latestLCPEntry?.renderTime || 0,
      lcpElement: latestLCPEntry?.element || null,
      lcpElementInfo: latestLCPEntry ? this.getElementInfo(latestLCPEntry.element) : null,
      renderBlockingResources: this.renderBlockingResources,
      criticalResourcesLoadTime: this.criticalResourcesLoadTime
    };

    diagnostics.recommendations = this.generateRecommendations(diagnostics);

    return diagnostics as LCPDiagnostics;
  }

  public startMonitoring() {
    // 页面加载完成后进行诊断
    if (document.readyState === 'complete') {
      this.performDiagnostics();
    } else {
      window.addEventListener('load', () => {
        setTimeout(() => this.performDiagnostics(), 1000);
      });
    }
  }

  private performDiagnostics() {
    const diagnostics = this.getDiagnostics();
    
    console.group('🔍 LCP 性能诊断报告');
    console.log('📊 LCP 数值:', diagnostics.lcpValue.toFixed(2) + 'ms');
    
    if (diagnostics.lcpElement) {
      console.log('🎯 LCP 元素:', diagnostics.lcpElement);
      console.log('📋 元素信息:', diagnostics.lcpElementInfo);
    }
    
    console.log('🚫 阻塞渲染资源数量:', diagnostics.renderBlockingResources.length);
    console.log('⏱️ 关键资源加载时间:', diagnostics.criticalResourcesLoadTime);
    
    if (diagnostics.recommendations.length > 0) {
      console.log('💡 优化建议:');
      diagnostics.recommendations.forEach((rec, index) => {
        console.log(`  ${index + 1}. ${rec}`);
      });
    }
    
    console.groupEnd();

    // 发送诊断数据到监控系统（如果需要）
    this.reportDiagnostics(diagnostics);
  }

  private reportDiagnostics(diagnostics: LCPDiagnostics) {
    // 这里可以将诊断数据发送到监控系统
    if (window.Logger) {
      window.Logger.log({
        et: 'performance',
        ei: 'lcp_diagnostics',
        en: 'LCP 性能诊断',
        pt: 'orderlist',
        params: {
          lcpValue: diagnostics.lcpValue,
          lcpElementTag: diagnostics.lcpElementInfo?.tagName,
          renderBlockingResourcesCount: diagnostics.renderBlockingResources.length,
          recommendationsCount: diagnostics.recommendations.length,
          kdtid: window._global?.kdtId
        }
      });
    }
  }

  public destroy() {
    if (this.observer) {
      this.observer.disconnect();
    }
    if (this.resourceObserver) {
      this.resourceObserver.disconnect();
    }
  }
}

// 创建全局 LCP 分析器实例
export const lcpAnalyzer = new LCPAnalyzer();

// 自动开始监控
if (typeof window !== 'undefined') {
  lcpAnalyzer.startMonitoring();
}

// 导出诊断函数供手动调用
export const getLCPDiagnostics = () => lcpAnalyzer.getDiagnostics();

// 页面卸载时清理
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    lcpAnalyzer.destroy();
  });
}
