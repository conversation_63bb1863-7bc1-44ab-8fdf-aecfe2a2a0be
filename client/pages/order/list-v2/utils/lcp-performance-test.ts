/**
 * LCP 性能测试工具
 * 用于自动化测试和验证 LCP 优化效果
 */

interface PerformanceTestResult {
  lcp: number;
  fcp: number;
  fid: number;
  cls: number;
  ttfb: number;
  domContentLoaded: number;
  loadComplete: number;
  resourceCount: number;
  totalResourceSize: number;
  timestamp: number;
  userAgent: string;
  connectionType?: string;
}

interface TestConfig {
  iterations?: number;
  warmupRuns?: number;
  timeout?: number;
  collectResourceTiming?: boolean;
  simulateSlowNetwork?: boolean;
}

class LCPPerformanceTester {
  private results: PerformanceTestResult[] = [];
  private observer: PerformanceObserver | null = null;
  private resourceObserver: PerformanceObserver | null = null;
  private currentTest: Partial<PerformanceTestResult> = {};

  constructor() {
    this.initObservers();
  }

  private initObservers() {
    if ('PerformanceObserver' in window) {
      // LCP Observer
      this.observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (entry.entryType === 'largest-contentful-paint') {
            this.currentTest.lcp = entry.startTime;
          } else if (entry.entryType === 'first-contentful-paint') {
            this.currentTest.fcp = entry.startTime;
          } else if (entry.entryType === 'first-input') {
            this.currentTest.fid = (entry as any).processingStart - entry.startTime;
          } else if (entry.entryType === 'layout-shift') {
            if (!(entry as any).hadRecentInput) {
              this.currentTest.cls = (this.currentTest.cls || 0) + (entry as any).value;
            }
          }
        });
      });

      try {
        this.observer.observe({ 
          entryTypes: ['largest-contentful-paint', 'first-contentful-paint', 'first-input', 'layout-shift'] 
        });
      } catch (e) {
        console.warn('Performance observer not fully supported:', e);
      }

      // Resource Observer
      if (this.observer) {
        this.resourceObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          let totalSize = 0;
          entries.forEach((entry) => {
            if (entry.entryType === 'resource') {
              const resourceEntry = entry as PerformanceResourceTiming;
              totalSize += resourceEntry.transferSize || 0;
            }
          });
          this.currentTest.resourceCount = entries.length;
          this.currentTest.totalResourceSize = totalSize;
        });

        try {
          this.resourceObserver.observe({ entryTypes: ['resource'] });
        } catch (e) {
          console.warn('Resource observer not supported:', e);
        }
      }
    }
  }

  /**
   * 运行单次性能测试
   */
  async runSingleTest(): Promise<PerformanceTestResult> {
    return new Promise((resolve) => {
      this.currentTest = {
        timestamp: Date.now(),
        userAgent: navigator.userAgent,
        connectionType: (navigator as any).connection?.effectiveType
      };

      // 获取导航时间
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      if (navigation) {
        this.currentTest.ttfb = navigation.responseStart - navigation.requestStart;
        this.currentTest.domContentLoaded = navigation.domContentLoadedEventEnd - navigation.navigationStart;
        this.currentTest.loadComplete = navigation.loadEventEnd - navigation.navigationStart;
      }

      // 等待页面加载完成后收集结果
      setTimeout(() => {
        const result: PerformanceTestResult = {
          lcp: this.currentTest.lcp || 0,
          fcp: this.currentTest.fcp || 0,
          fid: this.currentTest.fid || 0,
          cls: this.currentTest.cls || 0,
          ttfb: this.currentTest.ttfb || 0,
          domContentLoaded: this.currentTest.domContentLoaded || 0,
          loadComplete: this.currentTest.loadComplete || 0,
          resourceCount: this.currentTest.resourceCount || 0,
          totalResourceSize: this.currentTest.totalResourceSize || 0,
          timestamp: this.currentTest.timestamp || Date.now(),
          userAgent: this.currentTest.userAgent || '',
          connectionType: this.currentTest.connectionType
        };

        this.results.push(result);
        resolve(result);
      }, 3000); // 等待3秒确保所有指标都被收集
    });
  }

  /**
   * 运行多次测试并计算平均值
   */
  async runMultipleTests(config: TestConfig = {}): Promise<{
    average: PerformanceTestResult;
    median: PerformanceTestResult;
    results: PerformanceTestResult[];
    analysis: any;
  }> {
    const {
      iterations = 5,
      warmupRuns = 1,
      timeout = 30000
    } = config;

    console.log(`🚀 开始 LCP 性能测试 (预热: ${warmupRuns}次, 测试: ${iterations}次)`);

    // 预热运行
    for (let i = 0; i < warmupRuns; i++) {
      console.log(`🔥 预热运行 ${i + 1}/${warmupRuns}`);
      await this.runSingleTest();
      await this.delay(1000);
    }

    // 清空预热结果
    this.results = [];

    // 正式测试
    const testResults: PerformanceTestResult[] = [];
    for (let i = 0; i < iterations; i++) {
      console.log(`📊 测试运行 ${i + 1}/${iterations}`);
      try {
        const result = await Promise.race([
          this.runSingleTest(),
          this.timeoutPromise(timeout)
        ]);
        testResults.push(result);
        await this.delay(2000); // 测试间隔
      } catch (error) {
        console.warn(`测试 ${i + 1} 超时或失败:`, error);
      }
    }

    if (testResults.length === 0) {
      throw new Error('所有测试都失败了');
    }

    // 计算统计数据
    const average = this.calculateAverage(testResults);
    const median = this.calculateMedian(testResults);
    const analysis = this.analyzeResults(testResults);

    console.log('📈 测试完成，结果分析:', analysis);

    return {
      average,
      median,
      results: testResults,
      analysis
    };
  }

  /**
   * 计算平均值
   */
  private calculateAverage(results: PerformanceTestResult[]): PerformanceTestResult {
    const sum = results.reduce((acc, result) => ({
      lcp: acc.lcp + result.lcp,
      fcp: acc.fcp + result.fcp,
      fid: acc.fid + result.fid,
      cls: acc.cls + result.cls,
      ttfb: acc.ttfb + result.ttfb,
      domContentLoaded: acc.domContentLoaded + result.domContentLoaded,
      loadComplete: acc.loadComplete + result.loadComplete,
      resourceCount: acc.resourceCount + result.resourceCount,
      totalResourceSize: acc.totalResourceSize + result.totalResourceSize,
      timestamp: acc.timestamp,
      userAgent: acc.userAgent,
      connectionType: acc.connectionType
    }), {
      lcp: 0, fcp: 0, fid: 0, cls: 0, ttfb: 0,
      domContentLoaded: 0, loadComplete: 0, resourceCount: 0,
      totalResourceSize: 0, timestamp: Date.now(),
      userAgent: results[0]?.userAgent || '',
      connectionType: results[0]?.connectionType
    });

    const count = results.length;
    return {
      lcp: sum.lcp / count,
      fcp: sum.fcp / count,
      fid: sum.fid / count,
      cls: sum.cls / count,
      ttfb: sum.ttfb / count,
      domContentLoaded: sum.domContentLoaded / count,
      loadComplete: sum.loadComplete / count,
      resourceCount: Math.round(sum.resourceCount / count),
      totalResourceSize: Math.round(sum.totalResourceSize / count),
      timestamp: sum.timestamp,
      userAgent: sum.userAgent,
      connectionType: sum.connectionType
    };
  }

  /**
   * 计算中位数
   */
  private calculateMedian(results: PerformanceTestResult[]): PerformanceTestResult {
    const sorted = results.sort((a, b) => a.lcp - b.lcp);
    const mid = Math.floor(sorted.length / 2);
    return sorted.length % 2 === 0
      ? this.calculateAverage([sorted[mid - 1], sorted[mid]])
      : sorted[mid];
  }

  /**
   * 分析测试结果
   */
  private analyzeResults(results: PerformanceTestResult[]) {
    const average = this.calculateAverage(results);
    
    return {
      lcpGrade: this.getLCPGrade(average.lcp),
      fcpGrade: this.getFCPGrade(average.fcp),
      clsGrade: this.getCLSGrade(average.cls),
      recommendations: this.generateRecommendations(average),
      variability: this.calculateVariability(results),
      passesWebVitals: average.lcp <= 2500 && average.fid <= 100 && average.cls <= 0.1
    };
  }

  private getLCPGrade(lcp: number): string {
    if (lcp <= 2500) return 'Good';
    if (lcp <= 4000) return 'Needs Improvement';
    return 'Poor';
  }

  private getFCPGrade(fcp: number): string {
    if (fcp <= 1800) return 'Good';
    if (fcp <= 3000) return 'Needs Improvement';
    return 'Poor';
  }

  private getCLSGrade(cls: number): string {
    if (cls <= 0.1) return 'Good';
    if (cls <= 0.25) return 'Needs Improvement';
    return 'Poor';
  }

  private generateRecommendations(result: PerformanceTestResult): string[] {
    const recommendations: string[] = [];
    
    if (result.lcp > 2500) {
      recommendations.push('LCP 超过 2.5 秒，建议优化最大内容元素的加载');
    }
    
    if (result.fcp > 1800) {
      recommendations.push('FCP 超过 1.8 秒，建议优化关键渲染路径');
    }
    
    if (result.cls > 0.1) {
      recommendations.push('CLS 超过 0.1，建议减少布局偏移');
    }
    
    if (result.ttfb > 600) {
      recommendations.push('TTFB 超过 600ms，建议优化服务器响应时间');
    }
    
    return recommendations;
  }

  private calculateVariability(results: PerformanceTestResult[]) {
    const lcpValues = results.map(r => r.lcp);
    const mean = lcpValues.reduce((a, b) => a + b, 0) / lcpValues.length;
    const variance = lcpValues.reduce((a, b) => a + Math.pow(b - mean, 2), 0) / lcpValues.length;
    const standardDeviation = Math.sqrt(variance);
    
    return {
      mean,
      standardDeviation,
      coefficientOfVariation: standardDeviation / mean,
      isConsistent: standardDeviation / mean < 0.1 // CV < 10% 认为是一致的
    };
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private timeoutPromise(ms: number): Promise<never> {
    return new Promise((_, reject) => 
      setTimeout(() => reject(new Error('Test timeout')), ms)
    );
  }

  /**
   * 生成测试报告
   */
  generateReport(testResult: any): string {
    const { average, analysis } = testResult;
    
    return `
# LCP 性能测试报告

## 测试结果概览
- **LCP**: ${average.lcp.toFixed(2)}ms (${analysis.lcpGrade})
- **FCP**: ${average.fcp.toFixed(2)}ms (${analysis.fcpGrade})
- **CLS**: ${average.cls.toFixed(3)} (${analysis.clsGrade})
- **TTFB**: ${average.ttfb.toFixed(2)}ms
- **Web Vitals 通过**: ${analysis.passesWebVitals ? '✅' : '❌'}

## 详细指标
- DOM 内容加载完成: ${average.domContentLoaded.toFixed(2)}ms
- 页面完全加载: ${average.loadComplete.toFixed(2)}ms
- 资源数量: ${average.resourceCount}
- 总资源大小: ${(average.totalResourceSize / 1024).toFixed(2)}KB

## 优化建议
${analysis.recommendations.map((rec: string) => `- ${rec}`).join('\n')}

## 测试环境
- 用户代理: ${average.userAgent}
- 网络类型: ${average.connectionType || 'Unknown'}
- 测试时间: ${new Date(average.timestamp).toLocaleString()}
    `.trim();
  }

  destroy() {
    if (this.observer) {
      this.observer.disconnect();
    }
    if (this.resourceObserver) {
      this.resourceObserver.disconnect();
    }
  }
}

// 创建全局测试实例
export const lcpTester = new LCPPerformanceTester();

// 便捷的测试函数
export const runLCPTest = async (config?: TestConfig) => {
  return await lcpTester.runMultipleTests(config);
};

// 页面卸载时清理
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    lcpTester.destroy();
  });
}
