/**
 * 智能资源预加载器
 * 用于优化关键资源的加载时机，提升 LCP 性能
 */

interface PreloadResource {
  href: string;
  as: string;
  type?: string;
  crossOrigin?: string;
  media?: string;
  priority?: 'high' | 'low';
}

interface PrefetchResource {
  href: string;
  as?: string;
  type?: string;
}

class ResourcePreloader {
  private preloadedResources = new Set<string>();
  private prefetchedResources = new Set<string>();
  private observer: IntersectionObserver | null = null;

  constructor() {
    this.initIntersectionObserver();
  }

  /**
   * 预加载关键资源 (preload)
   * 用于当前页面立即需要的资源
   */
  preload(resources: PreloadResource[]): void {
    resources.forEach(resource => {
      if (this.preloadedResources.has(resource.href)) {
        return;
      }

      const link = document.createElement('link');
      link.rel = 'preload';
      link.href = resource.href;
      link.as = resource.as;
      
      if (resource.type) {
        link.type = resource.type;
      }
      
      if (resource.crossOrigin) {
        link.crossOrigin = resource.crossOrigin;
      }
      
      if (resource.media) {
        link.media = resource.media;
      }

      // 设置优先级（Chrome 支持）
      if (resource.priority && 'fetchPriority' in link) {
        (link as any).fetchPriority = resource.priority;
      }

      document.head.appendChild(link);
      this.preloadedResources.add(resource.href);
    });
  }

  /**
   * 预获取资源 (prefetch)
   * 用于下一个页面或用户可能访问的资源
   */
  prefetch(resources: PrefetchResource[]): void {
    // 只在空闲时进行预获取
    this.executeOnIdle(() => {
      resources.forEach(resource => {
        if (this.prefetchedResources.has(resource.href)) {
          return;
        }

        const link = document.createElement('link');
        link.rel = 'prefetch';
        link.href = resource.href;
        
        if (resource.as) {
          link.as = resource.as;
        }
        
        if (resource.type) {
          link.type = resource.type;
        }

        document.head.appendChild(link);
        this.prefetchedResources.add(resource.href);
      });
    });
  }

  /**
   * DNS 预解析
   */
  dnsPrefetch(domains: string[]): void {
    domains.forEach(domain => {
      const link = document.createElement('link');
      link.rel = 'dns-prefetch';
      link.href = domain;
      document.head.appendChild(link);
    });
  }

  /**
   * 预连接
   */
  preconnect(origins: Array<{ href: string; crossOrigin?: boolean }>): void {
    origins.forEach(origin => {
      const link = document.createElement('link');
      link.rel = 'preconnect';
      link.href = origin.href;
      
      if (origin.crossOrigin) {
        link.crossOrigin = '';
      }
      
      document.head.appendChild(link);
    });
  }

  /**
   * 智能图片预加载
   * 基于视口和用户行为预加载图片
   */
  preloadImages(selector: string): void {
    const images = document.querySelectorAll(selector);
    
    images.forEach(img => {
      if (this.observer) {
        this.observer.observe(img);
      }
    });
  }

  /**
   * 预加载关键 API 数据
   */
  preloadApiData(endpoints: string[]): void {
    this.executeOnIdle(() => {
      endpoints.forEach(endpoint => {
        fetch(endpoint, {
          method: 'GET',
          credentials: 'include',
          headers: {
            'X-Preload': 'true'
          }
        }).catch(() => {
          // 忽略预加载失败
        });
      });
    });
  }

  /**
   * 基于用户行为的智能预加载
   */
  smartPreload(): void {
    // 鼠标悬停时预加载
    document.addEventListener('mouseover', (e) => {
      const target = e.target as HTMLElement;
      const link = target.closest('a[href]') as HTMLAnchorElement;
      
      if (link && this.isInternalLink(link.href)) {
        this.prefetchPage(link.href);
      }
    });

    // 触摸开始时预加载（移动端）
    document.addEventListener('touchstart', (e) => {
      const target = e.target as HTMLElement;
      const link = target.closest('a[href]') as HTMLAnchorElement;
      
      if (link && this.isInternalLink(link.href)) {
        this.prefetchPage(link.href);
      }
    });
  }

  /**
   * 预加载页面资源
   */
  private prefetchPage(href: string): void {
    if (this.prefetchedResources.has(href)) {
      return;
    }

    // 预获取 HTML
    this.prefetch([{ href, as: 'document' }]);
    
    // 预获取可能的 JS 和 CSS 资源
    const basePath = href.split('/').slice(0, -1).join('/');
    this.prefetch([
      { href: `${basePath}/index.js`, as: 'script' },
      { href: `${basePath}/index.css`, as: 'style' }
    ]);
  }

  /**
   * 判断是否为内部链接
   */
  private isInternalLink(href: string): boolean {
    try {
      const url = new URL(href, window.location.origin);
      return url.origin === window.location.origin;
    } catch {
      return false;
    }
  }

  /**
   * 初始化 Intersection Observer
   */
  private initIntersectionObserver(): void {
    if ('IntersectionObserver' in window) {
      this.observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement;
            if (img.dataset.src) {
              this.preload([{
                href: img.dataset.src,
                as: 'image'
              }]);
            }
            this.observer?.unobserve(img);
          }
        });
      }, {
        rootMargin: '50px'
      });
    }
  }

  /**
   * 在浏览器空闲时执行
   */
  private executeOnIdle(callback: () => void): void {
    if ('requestIdleCallback' in window) {
      window.requestIdleCallback(callback, { timeout: 2000 });
    } else {
      setTimeout(callback, 1000);
    }
  }

  /**
   * 销毁预加载器
   */
  destroy(): void {
    if (this.observer) {
      this.observer.disconnect();
    }
  }
}

// 创建全局实例
export const resourcePreloader = new ResourcePreloader();

/**
 * 订单列表页面专用的预加载配置
 */
export const preloadOrderListResources = () => {
  // 预加载关键 CSS
  resourcePreloader.preload([
    {
      href: '/css/order-list.css',
      as: 'style',
      priority: 'high'
    },
    {
      href: '/css/zent.css',
      as: 'style',
      priority: 'high'
    }
  ]);

  // 预加载关键字体
  resourcePreloader.preload([
    {
      href: '/fonts/PingFangSC-Regular.woff2',
      as: 'font',
      type: 'font/woff2',
      crossOrigin: 'anonymous',
      priority: 'high'
    }
  ]);

  // 预加载关键图标
  resourcePreloader.preload([
    {
      href: '/images/order-icons.svg',
      as: 'image',
      priority: 'high'
    }
  ]);

  // DNS 预解析
  resourcePreloader.dnsPrefetch([
    '//cdn.youzan.com',
    '//img.yzcdn.cn',
    '//b.yzcdn.cn',
    '//s.yzcdn.cn'
  ]);

  // 预连接关键域名
  resourcePreloader.preconnect([
    { href: '//cdn.youzan.com', crossOrigin: true },
    { href: '//img.yzcdn.cn', crossOrigin: true }
  ]);

  // 预加载可能的 API 端点
  resourcePreloader.preloadApiData([
    '/v4/trade/order/source/list',
    '/v4/trade/store/list'
  ]);

  // 启用智能预加载
  resourcePreloader.smartPreload();
};

/**
 * 基于网络状况的自适应预加载
 */
export const adaptivePreload = () => {
  // 检查网络连接状况
  const connection = (navigator as any).connection;
  
  if (connection) {
    const { effectiveType, saveData } = connection;
    
    // 在慢速网络或省流量模式下减少预加载
    if (effectiveType === 'slow-2g' || effectiveType === '2g' || saveData) {
      return;
    }
    
    // 在快速网络下增加预加载
    if (effectiveType === '4g') {
      resourcePreloader.prefetch([
        { href: '/js/order-detail.js', as: 'script' },
        { href: '/js/order-export.js', as: 'script' }
      ]);
    }
  }
};

// 页面卸载时清理
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    resourcePreloader.destroy();
  });
}
