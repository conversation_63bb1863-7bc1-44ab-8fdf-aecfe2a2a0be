import React, { useState, useRef, useEffect, useCallback } from 'react';
import './optimized-image.scss';

interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  placeholder?: string;
  priority?: boolean;
  sizes?: string;
  quality?: number;
  onLoad?: () => void;
  onError?: () => void;
}

/**
 * 优化的图片组件
 * 支持 WebP 格式、懒加载、响应式图片等特性
 */
export const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  width,
  height,
  className = '',
  placeholder,
  priority = false,
  sizes,
  quality = 80,
  onLoad,
  onError
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isError, setIsError] = useState(false);
  const [currentSrc, setCurrentSrc] = useState<string>('');
  const imgRef = useRef<HTMLImageElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  // 生成 WebP 和原始格式的 URL
  const generateImageUrls = useCallback((originalSrc: string) => {
    const webpSrc = originalSrc.replace(/\.(jpg|jpeg|png)$/i, '.webp');
    const optimizedSrc = `${originalSrc}?quality=${quality}${width ? `&width=${width}` : ''}${height ? `&height=${height}` : ''}`;
    
    return {
      webp: webpSrc,
      original: optimizedSrc,
      fallback: originalSrc
    };
  }, [quality, width, height]);

  // 检查 WebP 支持
  const supportsWebP = useCallback(() => {
    const canvas = document.createElement('canvas');
    canvas.width = 1;
    canvas.height = 1;
    return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
  }, []);

  // 加载图片
  const loadImage = useCallback(() => {
    if (!src || currentSrc) return;

    const urls = generateImageUrls(src);
    const targetSrc = supportsWebP() ? urls.webp : urls.original;

    // 创建新的 Image 对象进行预加载
    const img = new Image();
    
    img.onload = () => {
      setCurrentSrc(targetSrc);
      setIsLoaded(true);
      onLoad?.();
    };
    
    img.onerror = () => {
      // WebP 加载失败时回退到原始格式
      if (targetSrc.includes('.webp')) {
        const fallbackImg = new Image();
        fallbackImg.onload = () => {
          setCurrentSrc(urls.original);
          setIsLoaded(true);
          onLoad?.();
        };
        fallbackImg.onerror = () => {
          setIsError(true);
          onError?.();
        };
        fallbackImg.src = urls.original;
      } else {
        setIsError(true);
        onError?.();
      }
    };
    
    img.src = targetSrc;
  }, [src, currentSrc, generateImageUrls, supportsWebP, onLoad, onError]);

  // 初始化 Intersection Observer
  useEffect(() => {
    if (priority) {
      // 高优先级图片立即加载
      loadImage();
      return;
    }

    if ('IntersectionObserver' in window) {
      observerRef.current = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              loadImage();
              observerRef.current?.unobserve(entry.target);
            }
          });
        },
        {
          rootMargin: '50px'
        }
      );

      if (imgRef.current) {
        observerRef.current.observe(imgRef.current);
      }
    } else {
      // 降级方案：直接加载
      loadImage();
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [priority, loadImage]);

  // 生成 srcSet 用于响应式图片
  const generateSrcSet = useCallback((baseSrc: string) => {
    if (!width) return undefined;

    const urls = generateImageUrls(baseSrc);
    const srcSet = [
      `${urls.original}&width=${width} 1x`,
      `${urls.original}&width=${width * 2} 2x`,
      `${urls.original}&width=${width * 3} 3x`
    ];

    return srcSet.join(', ');
  }, [width, generateImageUrls]);

  // 渲染占位符
  const renderPlaceholder = () => {
    if (placeholder) {
      return (
        <img
          src={placeholder}
          alt=""
          className="optimized-image__placeholder"
          style={{ width, height }}
        />
      );
    }

    return (
      <div
        className="optimized-image__skeleton"
        style={{ width, height }}
      >
        <div className="optimized-image__skeleton-content" />
      </div>
    );
  };

  // 渲染错误状态
  const renderError = () => (
    <div
      className="optimized-image__error"
      style={{ width, height }}
    >
      <div className="optimized-image__error-icon">📷</div>
      <div className="optimized-image__error-text">图片加载失败</div>
    </div>
  );

  if (isError) {
    return renderError();
  }

  return (
    <div className={`optimized-image ${className}`}>
      {!isLoaded && renderPlaceholder()}
      
      <img
        ref={imgRef}
        src={currentSrc || (priority ? src : undefined)}
        srcSet={currentSrc ? generateSrcSet(src) : undefined}
        sizes={sizes}
        alt={alt}
        width={width}
        height={height}
        className={`optimized-image__img ${isLoaded ? 'optimized-image__img--loaded' : ''}`}
        loading={priority ? 'eager' : 'lazy'}
        decoding="async"
        style={{
          opacity: isLoaded ? 1 : 0,
          transition: 'opacity 0.3s ease'
        }}
      />
    </div>
  );
};

/**
 * 商品图片组件
 * 专门用于订单列表中的商品图片
 */
export const ProductImage: React.FC<{
  src: string;
  alt: string;
  size?: 'small' | 'medium' | 'large';
  className?: string;
}> = ({ src, alt, size = 'medium', className = '' }) => {
  const sizeMap = {
    small: { width: 40, height: 40 },
    medium: { width: 60, height: 60 },
    large: { width: 80, height: 80 }
  };

  const { width, height } = sizeMap[size];

  return (
    <OptimizedImage
      src={src}
      alt={alt}
      width={width}
      height={height}
      className={`product-image product-image--${size} ${className}`}
      placeholder="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0xMiAxNkwyMCAyNEwyOCAxNiIgc3Ryb2tlPSIjQ0NDIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K"
      quality={85}
    />
  );
};

/**
 * 头像图片组件
 */
export const AvatarImage: React.FC<{
  src: string;
  alt: string;
  size?: number;
  className?: string;
}> = ({ src, alt, size = 32, className = '' }) => {
  return (
    <OptimizedImage
      src={src}
      alt={alt}
      width={size}
      height={size}
      className={`avatar-image ${className}`}
      placeholder={`data:image/svg+xml;base64,${btoa(`
        <svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" fill="none" xmlns="http://www.w3.org/2000/svg">
          <circle cx="${size/2}" cy="${size/2}" r="${size/2}" fill="#E5E5E5"/>
          <path d="M${size/2-6} ${size/2-2}C${size/2-6} ${size/2-5} ${size/2-3} ${size/2-8} ${size/2} ${size/2-8}C${size/2+3} ${size/2-8} ${size/2+6} ${size/2-5} ${size/2+6} ${size/2-2}C${size/2+6} ${size/2+1} ${size/2+3} ${size/2+4} ${size/2} ${size/2+4}C${size/2-3} ${size/2+4} ${size/2-6} ${size/2+1} ${size/2-6} ${size/2-2}Z" fill="#CCC"/>
          <path d="M${size/2-8} ${size/2+6}C${size/2-8} ${size/2+3} ${size/2-5} ${size/2} ${size/2} ${size/2}C${size/2+5} ${size/2} ${size/2+8} ${size/2+3} ${size/2+8} ${size/2+6}" stroke="#CCC" stroke-width="2" stroke-linecap="round"/>
        </svg>
      `)}`}
      quality={90}
    />
  );
};

export default OptimizedImage;
