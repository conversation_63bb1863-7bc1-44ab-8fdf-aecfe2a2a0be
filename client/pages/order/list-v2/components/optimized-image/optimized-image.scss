/* 优化图片组件样式 */
.optimized-image {
  position: relative;
  display: inline-block;
  overflow: hidden;
  
  &__img {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: opacity 0.3s ease;
    
    &--loaded {
      opacity: 1;
    }
  }
  
  &__placeholder {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    filter: blur(5px);
    transition: opacity 0.3s ease;
  }
  
  &__skeleton {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
    
    &-content {
      width: 50%;
      height: 50%;
      background: linear-gradient(90deg, #e0e0e0 25%, #f0f0f0 50%, #e0e0e0 75%);
      background-size: 200px 100%;
      animation: skeleton-loading 1.5s ease-in-out infinite;
      border-radius: 4px;
    }
  }
  
  &__error {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #f8f8f8;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #999;
    font-size: 12px;
    
    &-icon {
      font-size: 24px;
      margin-bottom: 4px;
      opacity: 0.5;
    }
    
    &-text {
      font-size: 10px;
      text-align: center;
    }
  }
}

@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* 商品图片样式 */
.product-image {
  border-radius: 4px;
  border: 1px solid #e8e8e8;
  background: #fff;
  
  &--small {
    width: 40px;
    height: 40px;
  }
  
  &--medium {
    width: 60px;
    height: 60px;
  }
  
  &--large {
    width: 80px;
    height: 80px;
  }
  
  .optimized-image__img {
    border-radius: 3px;
  }
  
  .optimized-image__error {
    border-radius: 3px;
    
    &-icon {
      font-size: 16px;
    }
    
    &-text {
      font-size: 8px;
    }
  }
}

/* 头像图片样式 */
.avatar-image {
  border-radius: 50%;
  border: 1px solid #e8e8e8;
  background: #fff;
  
  .optimized-image__img {
    border-radius: 50%;
  }
  
  .optimized-image__placeholder,
  .optimized-image__skeleton,
  .optimized-image__error {
    border-radius: 50%;
  }
  
  .optimized-image__error {
    &-icon {
      font-size: 14px;
    }
    
    &-text {
      display: none; /* 头像错误时不显示文字 */
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .product-image {
    &--small {
      width: 36px;
      height: 36px;
    }
    
    &--medium {
      width: 50px;
      height: 50px;
    }
    
    &--large {
      width: 70px;
      height: 70px;
    }
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .optimized-image {
    &__skeleton {
      background: #d0d0d0;
      
      &-content {
        background: linear-gradient(90deg, #a0a0a0 25%, #c0c0c0 50%, #a0a0a0 75%);
        background-size: 200px 100%;
      }
    }
    
    &__error {
      background: #e0e0e0;
      color: #333;
    }
  }
  
  .product-image,
  .avatar-image {
    border-color: #999;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .optimized-image {
    &__img,
    &__placeholder {
      transition: none;
    }
    
    &__skeleton-content {
      animation: none;
      background: #e0e0e0;
    }
  }
}

/* 暗色主题 */
@media (prefers-color-scheme: dark) {
  .optimized-image {
    &__skeleton {
      background: #2a2a2a;
      
      &-content {
        background: linear-gradient(90deg, #3a3a3a 25%, #4a4a4a 50%, #3a3a3a 75%);
        background-size: 200px 100%;
      }
    }
    
    &__error {
      background: #1a1a1a;
      color: #ccc;
    }
  }
  
  .product-image,
  .avatar-image {
    border-color: #444;
    background: #2a2a2a;
  }
}

/* 打印样式 */
@media print {
  .optimized-image {
    &__skeleton,
    &__placeholder {
      display: none;
    }
    
    &__error {
      border: 1px solid #ccc;
      
      &-icon {
        display: none;
      }
      
      &-text {
        color: #666;
        font-size: 8px;
      }
    }
  }
}
