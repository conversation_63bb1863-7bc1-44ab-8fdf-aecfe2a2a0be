import React, { lazy, Suspense } from 'react';
import { BlockLoading } from 'zent';

// 懒加载组件的通用加载器
const LazyComponentLoader: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <Suspense fallback={<BlockLoading loading={true} />}>
    {children}
  </Suspense>
);

// 懒加载的对话框组件
export const LazyNewExportDialog = lazy(() => import('./new-export-dialog'));
export const LazyRechargeDialog = lazy(() => import('./recharge-dialog'));
export const LazyStudentInfoDialog = lazy(() => import('./student-info-dialog'));

// 懒加载的批量操作组件
export const LazyBatchDeliveryPrintButton = lazy(() => import('./batch-delivery-print-button'));

// 懒加载的订单操作组件
export const LazySignFor = lazy(() => import('./sign-for'));

// 懒加载的提醒组件
export const LazyCityOrderNotice = lazy(() => import('./city-order-notice'));
export const LazyAlertOverTimeOrder = lazy(() => import('./alert-over-time-order'));

// 包装器组件，提供统一的懒加载体验
export const withLazyLoading = <P extends object>(
  Component: React.ComponentType<P>
): React.FC<P> => {
  return (props: P) => (
    <LazyComponentLoader>
      <Component {...props} />
    </LazyComponentLoader>
  );
};

// 预加载函数，用于在用户交互前预加载组件
export const preloadComponents = {
  exportDialog: () => import('./new-export-dialog'),
  rechargeDialog: () => import('./recharge-dialog'),
  studentInfoDialog: () => import('./student-info-dialog'),
  batchDeliveryPrint: () => import('./batch-delivery-print-button'),
  signFor: () => import('./sign-for'),
  cityOrderNotice: () => import('./city-order-notice'),
  alertOverTimeOrder: () => import('./alert-over-time-order'),
};

// 智能预加载：根据用户行为预加载相关组件
export const useSmartPreload = () => {
  React.useEffect(() => {
    // 在页面空闲时预加载常用组件
    const preloadOnIdle = () => {
      if ('requestIdleCallback' in window) {
        window.requestIdleCallback(() => {
          preloadComponents.exportDialog();
          preloadComponents.batchDeliveryPrint();
        });
      } else {
        // 降级方案：使用 setTimeout
        setTimeout(() => {
          preloadComponents.exportDialog();
          preloadComponents.batchDeliveryPrint();
        }, 2000);
      }
    };

    preloadOnIdle();
  }, []);
};

export default LazyComponentLoader;
