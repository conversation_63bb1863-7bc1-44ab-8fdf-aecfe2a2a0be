import React from 'react';

/**
 * 关键 CSS 内联组件
 * 将关键的 CSS 样式内联到 HTML 中，避免阻塞渲染
 */
export const CriticalCSS: React.FC = () => {
  return (
    <style
      dangerouslySetInnerHTML={{
        __html: `
          /* 关键渲染路径 CSS - 仅包含首屏必需的样式 */
          
          /* 基础布局样式 */
          .app-inner {
            background: none;
            margin: 0 !important;
            min-height: 100vh;
          }
          
          /* 筛选面板基础样式 */
          .filter-panel {
            background-color: #fff;
            margin-bottom: 15px;
            padding: 20px 15px;
            box-sizing: border-box;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
          }
          
          /* 结果面板基础样式 */
          .result-panel-wrap {
            min-height: 400px; /* 预留空间避免布局偏移 */
          }
          
          .result-panel {
            background-color: #fff;
            padding: 15px;
            box-sizing: border-box;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
          }
          
          /* 订单列表容器基础样式 */
          .order-list {
            position: relative;
            min-height: 300px; /* 预留空间 */
          }
          
          .order-list-body {
            position: relative;
          }
          
          /* 加载状态样式 */
          .order-list-skeleton {
            padding: 16px 0;
          }
          
          .skeleton-item {
            display: flex;
            padding: 16px;
            border-bottom: 1px solid #f0f0f0;
            animation: skeleton-loading 1.5s ease-in-out infinite;
          }
          
          .skeleton-avatar {
            width: 48px;
            height: 48px;
            background: #f0f0f0;
            border-radius: 4px;
            margin-right: 12px;
            flex-shrink: 0;
          }
          
          .skeleton-content {
            flex: 1;
          }
          
          .skeleton-line {
            height: 16px;
            background: #f0f0f0;
            border-radius: 4px;
            margin-bottom: 8px;
          }
          
          .skeleton-line:nth-child(1) { width: 60%; }
          .skeleton-line:nth-child(2) { width: 80%; }
          .skeleton-line:nth-child(3) { width: 40%; }
          
          @keyframes skeleton-loading {
            0% { opacity: 1; }
            50% { opacity: 0.6; }
            100% { opacity: 1; }
          }
          
          /* 空状态样式 */
          .table-empty-content {
            text-align: center;
            padding: 60px 20px;
            color: #999;
          }
          
          .table-empty-content__query-icon {
            display: inline-block;
            width: 64px;
            height: 64px;
            background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTMyIDhDMTguNzQ1IDggOCAyMC43NDUgOCAzNEM4IDQ3LjI1NSAxOC43NDUgNjAgMzIgNjBDNDUuMjU1IDYwIDU2IDQ3LjI1NSA1NiAzNEM1NiAyMC43NDUgNDUuMjU1IDggMzIgOFpNMzIgNTJDMjMuMTYzIDUyIDE2IDQ0LjgzNyAxNiAzNkMxNiAyNy4xNjMgMjMuMTYzIDIwIDMyIDIwQzQwLjgzNyAyMCA0OCAyNy4xNjMgNDggMzZDNDggNDQuODM3IDQwLjgzNyA1MiAzMiA1MloiIGZpbGw9IiNEOUQ5RDkiLz4KPC9zdmc+') no-repeat center;
            background-size: contain;
            margin-bottom: 16px;
          }
          
          /* 批量操作区域 */
          .batch-remark {
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 16px;
          }
          
          /* 分页器基础样式 */
          .order-list-pagination {
            margin-top: 16px;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            min-height: 32px; /* 预留空间 */
          }
          
          /* 响应式设计 */
          @media (max-width: 768px) {
            .filter-panel,
            .result-panel {
              padding: 12px;
              margin-bottom: 12px;
            }
            
            .skeleton-item {
              padding: 12px;
            }
            
            .skeleton-avatar {
              width: 40px;
              height: 40px;
            }
          }
          
          /* 字体优化 */
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            font-display: swap;
          }
          
          /* 防止布局偏移的占位样式 */
          .layout-placeholder {
            background: #f8f9fa;
            border-radius: 4px;
            animation: placeholder-shimmer 2s ease-in-out infinite;
          }
          
          @keyframes placeholder-shimmer {
            0% { background-position: -200px 0; }
            100% { background-position: calc(200px + 100%) 0; }
          }
          
          .layout-placeholder {
            background: linear-gradient(90deg, #f8f9fa 25%, #e9ecef 50%, #f8f9fa 75%);
            background-size: 200px 100%;
          }
        `
      }}
    />
  );
};

/**
 * 预加载关键资源的组件
 */
export const ResourcePreloader: React.FC = () => {
  return (
    <>
      {/* 预加载关键字体 */}
      <link
        rel="preload"
        href="/fonts/PingFangSC-Regular.woff2"
        as="font"
        type="font/woff2"
        crossOrigin="anonymous"
      />
      
      {/* 预加载关键图片 */}
      <link
        rel="preload"
        href="/images/order-list-icons.svg"
        as="image"
      />
      
      {/* DNS 预解析 */}
      <link rel="dns-prefetch" href="//cdn.youzan.com" />
      <link rel="dns-prefetch" href="//img.yzcdn.cn" />
      <link rel="dns-prefetch" href="//b.yzcdn.cn" />
      
      {/* 预连接关键域名 */}
      <link rel="preconnect" href="//cdn.youzan.com" crossOrigin="" />
      <link rel="preconnect" href="//img.yzcdn.cn" crossOrigin="" />
    </>
  );
};

export default CriticalCSS;
