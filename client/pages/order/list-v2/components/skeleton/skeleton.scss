/* 骨架屏基础样式 */
.skeleton-line {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.5s ease-in-out infinite;
  border-radius: 4px;
  height: 16px;
  display: inline-block;
}

@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* 订单列表骨架屏 */
.order-list-skeleton {
  .skeleton-header {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
    background: #fafafa;
    
    .skeleton-header-item {
      margin-right: 20px;
      height: 14px;
      
      &:last-child {
        margin-right: 0;
      }
    }
  }
  
  .skeleton-list {
    .skeleton-item {
      display: flex;
      align-items: center;
      padding: 16px;
      border-bottom: 1px solid #f0f0f0;
      
      .skeleton-checkbox {
        margin-right: 12px;
        flex-shrink: 0;
      }
      
      .skeleton-order-info {
        width: 140px;
        margin-right: 20px;
        flex-shrink: 0;
      }
      
      .skeleton-goods {
        display: flex;
        align-items: center;
        flex: 1;
        margin-right: 20px;
        
        .skeleton-avatar {
          width: 48px;
          height: 48px;
          background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
          background-size: 200px 100%;
          animation: skeleton-loading 1.5s ease-in-out infinite;
          border-radius: 4px;
          margin-right: 12px;
          flex-shrink: 0;
        }
        
        .skeleton-goods-info {
          flex: 1;
        }
      }
      
      .skeleton-customer {
        width: 110px;
        margin-right: 20px;
        flex-shrink: 0;
      }
      
      .skeleton-amount {
        width: 80px;
        margin-right: 20px;
        flex-shrink: 0;
      }
      
      .skeleton-status {
        width: 60px;
        margin-right: 20px;
        flex-shrink: 0;
      }
      
      .skeleton-actions {
        display: flex;
        align-items: center;
        width: 120px;
        flex-shrink: 0;
      }
    }
  }
  
  .skeleton-pagination {
    display: flex;
    justify-content: flex-end;
    padding: 16px;
  }
}

/* 筛选器骨架屏 */
.filter-skeleton {
  .skeleton-filter-row {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .skeleton-filter-actions {
    display: flex;
    align-items: center;
    margin-top: 20px;
  }
}

/* 子筛选器骨架屏 */
.sub-filter-skeleton {
  margin-bottom: 20px;
  
  .skeleton-tabs {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
  }
  
  .skeleton-selectors {
    display: flex;
    align-items: center;
  }
}

/* 批量操作骨架屏 */
.batch-actions-skeleton {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

/* 页面骨架屏 */
.page-skeleton {
  .filter-panel {
    background-color: #fff;
    margin-bottom: 15px;
    padding: 20px 15px;
    box-sizing: border-box;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  }
  
  .result-panel-wrap {
    .result-panel {
      background-color: #fff;
      padding: 15px;
      box-sizing: border-box;
      border-radius: 4px;
      box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .order-list-skeleton .skeleton-list .skeleton-item {
    .skeleton-order-info {
      width: 120px;
    }
    
    .skeleton-customer {
      width: 90px;
    }
    
    .skeleton-amount {
      width: 70px;
    }
    
    .skeleton-actions {
      width: 100px;
    }
  }
}

@media (max-width: 768px) {
  .order-list-skeleton .skeleton-list .skeleton-item {
    flex-direction: column;
    align-items: flex-start;
    padding: 12px;
    
    .skeleton-checkbox,
    .skeleton-order-info,
    .skeleton-goods,
    .skeleton-customer,
    .skeleton-amount,
    .skeleton-status,
    .skeleton-actions {
      width: 100%;
      margin-right: 0;
      margin-bottom: 8px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
    
    .skeleton-goods {
      .skeleton-avatar {
        width: 40px;
        height: 40px;
      }
    }
  }
  
  .filter-skeleton .skeleton-filter-row {
    flex-direction: column;
    align-items: flex-start;
    
    .skeleton-line {
      margin-right: 0;
      margin-bottom: 8px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
  
  .sub-filter-skeleton {
    .skeleton-tabs,
    .skeleton-selectors {
      flex-wrap: wrap;
      
      .skeleton-line {
        margin-bottom: 8px;
      }
    }
  }
  
  .batch-actions-skeleton {
    flex-wrap: wrap;
    
    .skeleton-line {
      margin-bottom: 8px;
    }
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .skeleton-line,
  .skeleton-avatar {
    background: linear-gradient(90deg, #d0d0d0 25%, #b0b0b0 50%, #d0d0d0 75%);
    background-size: 200px 100%;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .skeleton-line,
  .skeleton-avatar {
    animation: none;
    background: #f0f0f0;
  }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  .skeleton-line,
  .skeleton-avatar {
    background: linear-gradient(90deg, #2a2a2a 25%, #3a3a3a 50%, #2a2a2a 75%);
    background-size: 200px 100%;
  }
  
  .order-list-skeleton .skeleton-header {
    background: #1a1a1a;
    border-bottom-color: #3a3a3a;
  }
  
  .order-list-skeleton .skeleton-list .skeleton-item {
    border-bottom-color: #3a3a3a;
  }
}
