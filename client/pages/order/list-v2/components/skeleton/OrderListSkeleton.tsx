import React from 'react';
import './skeleton.scss';

interface SkeletonProps {
  rows?: number;
  showHeader?: boolean;
  showPagination?: boolean;
}

/**
 * 订单列表骨架屏组件
 * 用于在数据加载时提供视觉占位，改善 LCP 性能
 */
export const OrderListSkeleton: React.FC<SkeletonProps> = ({ 
  rows = 5, 
  showHeader = true, 
  showPagination = true 
}) => {
  return (
    <div className="order-list-skeleton">
      {showHeader && <SkeletonHeader />}
      <div className="skeleton-list">
        {Array.from({ length: rows }, (_, index) => (
          <SkeletonItem key={index} />
        ))}
      </div>
      {showPagination && <SkeletonPagination />}
    </div>
  );
};

/**
 * 骨架屏表头
 */
const SkeletonHeader: React.FC = () => (
  <div className="skeleton-header">
    <div className="skeleton-header-item skeleton-line" style={{ width: '120px' }} />
    <div className="skeleton-header-item skeleton-line" style={{ width: '100px' }} />
    <div className="skeleton-header-item skeleton-line" style={{ width: '80px' }} />
    <div className="skeleton-header-item skeleton-line" style={{ width: '150px' }} />
    <div className="skeleton-header-item skeleton-line" style={{ width: '100px' }} />
    <div className="skeleton-header-item skeleton-line" style={{ width: '80px' }} />
  </div>
);

/**
 * 骨架屏列表项
 */
const SkeletonItem: React.FC = () => (
  <div className="skeleton-item">
    <div className="skeleton-checkbox">
      <div className="skeleton-line" style={{ width: '16px', height: '16px' }} />
    </div>
    <div className="skeleton-order-info">
      <div className="skeleton-line" style={{ width: '140px', marginBottom: '8px' }} />
      <div className="skeleton-line" style={{ width: '100px' }} />
    </div>
    <div className="skeleton-goods">
      <div className="skeleton-avatar" />
      <div className="skeleton-goods-info">
        <div className="skeleton-line" style={{ width: '200px', marginBottom: '6px' }} />
        <div className="skeleton-line" style={{ width: '120px' }} />
      </div>
    </div>
    <div className="skeleton-customer">
      <div className="skeleton-line" style={{ width: '80px', marginBottom: '6px' }} />
      <div className="skeleton-line" style={{ width: '110px' }} />
    </div>
    <div className="skeleton-amount">
      <div className="skeleton-line" style={{ width: '60px', marginBottom: '6px' }} />
      <div className="skeleton-line" style={{ width: '80px' }} />
    </div>
    <div className="skeleton-status">
      <div className="skeleton-line" style={{ width: '60px' }} />
    </div>
    <div className="skeleton-actions">
      <div className="skeleton-line" style={{ width: '50px', height: '28px', marginRight: '8px' }} />
      <div className="skeleton-line" style={{ width: '50px', height: '28px' }} />
    </div>
  </div>
);

/**
 * 骨架屏分页器
 */
const SkeletonPagination: React.FC = () => (
  <div className="skeleton-pagination">
    <div className="skeleton-line" style={{ width: '200px', height: '32px' }} />
  </div>
);

/**
 * 筛选器骨架屏
 */
export const FilterSkeleton: React.FC = () => (
  <div className="filter-skeleton">
    <div className="skeleton-filter-row">
      <div className="skeleton-line" style={{ width: '100px', marginRight: '12px' }} />
      <div className="skeleton-line" style={{ width: '200px', marginRight: '20px' }} />
      <div className="skeleton-line" style={{ width: '100px', marginRight: '12px' }} />
      <div className="skeleton-line" style={{ width: '150px' }} />
    </div>
    <div className="skeleton-filter-row">
      <div className="skeleton-line" style={{ width: '80px', marginRight: '12px' }} />
      <div className="skeleton-line" style={{ width: '180px', marginRight: '20px' }} />
      <div className="skeleton-line" style={{ width: '120px', marginRight: '12px' }} />
      <div className="skeleton-line" style={{ width: '100px' }} />
    </div>
    <div className="skeleton-filter-actions">
      <div className="skeleton-line" style={{ width: '60px', height: '32px', marginRight: '12px' }} />
      <div className="skeleton-line" style={{ width: '60px', height: '32px' }} />
    </div>
  </div>
);

/**
 * 子筛选器骨架屏
 */
export const SubFilterSkeleton: React.FC = () => (
  <div className="sub-filter-skeleton">
    <div className="skeleton-tabs">
      {Array.from({ length: 4 }, (_, index) => (
        <div 
          key={index} 
          className="skeleton-line" 
          style={{ 
            width: `${60 + index * 10}px`, 
            height: '32px', 
            marginRight: '12px' 
          }} 
        />
      ))}
    </div>
    <div className="skeleton-selectors">
      {Array.from({ length: 4 }, (_, index) => (
        <div 
          key={index} 
          className="skeleton-line" 
          style={{ 
            width: `${80 + index * 20}px`, 
            height: '28px', 
            marginRight: '12px' 
          }} 
        />
      ))}
    </div>
  </div>
);

/**
 * 批量操作骨架屏
 */
export const BatchActionsSkeleton: React.FC = () => (
  <div className="batch-actions-skeleton">
    <div className="skeleton-line" style={{ width: '16px', height: '16px', marginRight: '8px' }} />
    <div className="skeleton-line" style={{ width: '120px', marginRight: '16px' }} />
    <div className="skeleton-line" style={{ width: '80px', height: '32px', marginRight: '12px' }} />
    <div className="skeleton-line" style={{ width: '80px', height: '32px' }} />
  </div>
);

/**
 * 完整页面骨架屏
 */
export const PageSkeleton: React.FC = () => (
  <div className="page-skeleton">
    <div className="filter-panel">
      <FilterSkeleton />
    </div>
    <div className="result-panel-wrap">
      <div className="result-panel">
        <SubFilterSkeleton />
        <BatchActionsSkeleton />
        <OrderListSkeleton rows={8} showHeader={true} showPagination={true} />
      </div>
    </div>
  </div>
);

export default OrderListSkeleton;
