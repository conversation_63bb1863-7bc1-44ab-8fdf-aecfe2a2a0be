import React, { Component, useCallback, useMemo } from 'react';
import { TIME_SORT_OPTIONS } from '../../constants';
import { IOrderSearchQuery } from '../../types';
import SubFilterSelect from './components/Select';
import { QUICK_STATE, SELLER_REMARK, BUYER_MEMO_LIST, STAR_LIST } from './contants';

import './sub-filter.scss';

export interface IProps {
  onFilterConfirm: (filterOptions?: IOrderSearchQuery) => void;
  onFilterChange: (name: IOrderSearchQuery | string, value: any) => void;
  filterOptions: IOrderSearchQuery;
}

export interface IState {
  timeSortValue: string;
}

export type QuickState = 'topay' | 'tosend' | 'refunding';

// 使用 React.memo 优化组件，避免不必要的重渲染
const SubFilter: React.FC<IProps> = React.memo(({ onFilterChange, onFilterConfirm, filterOptions }) => {
  // 使用 useCallback 缓存状态变更处理函数
  const handleChangeState = useCallback((state: QuickState) => {
    if (filterOptions.state !== 'all') return;
    const states = [state];
    onFilterChange('states', states);

    window.Logger &&
      window.Logger.log({
        et: 'click', // 事件类型
        ei: 'secondary_screening_click', // 事件标识
        en: '二级筛选点击', // 事件名称
        pt: 'orderlist', // 页面类型
        params: {
          filter: QUICK_STATE.find((item) => item.value === state)?.text,
          kdtid: window._global.kdtId,
        }, // 事件参数
      });

    setTimeout(() => {
      onFilterConfirm({
        ...filterOptions,
        states,
        timestamp: Date.now(),
      });
    }, 100);
  }, [filterOptions, onFilterChange, onFilterConfirm]);

  // 使用 useMemo 缓存状态标签的渲染
  const stateTabsElements = useMemo(() => {
    return QUICK_STATE.map(({ text, value }) => {
      const isSelected = (filterOptions.states || []).indexOf(value) !== -1;
      const classNameList = ['order-state-tab'];
      if (isSelected) {
        classNameList.push('order-state-tab__active');
      }
      if (filterOptions.state !== 'all') {
        classNameList.push('order-state-tab__disabled');
      }
      return (
        <span
          key={value}
          onClick={() => handleChangeState(value as QuickState)}
          className={classNameList.join(' ')}
        >
          {text}
        </span>
      );
    });
  }, [filterOptions.states, filterOptions.state, handleChangeState]);

  return (
    <div className="sub-filter-wrap">
      <div className="order-state-tab-wrap">
        {stateTabsElements}
      </div>
      <div className="mini-selector-group">
        <SubFilterSelect
          placeholder="是否备注"
          filterKey="sellerRemark"
          width={110}
          data={SELLER_REMARK}
          value={filterOptions.sellerRemark}
          filterOptions={filterOptions}
          onFilterChange={onFilterChange}
          onFilterConfirm={onFilterConfirm}
        />
        <SubFilterSelect
          placeholder="是否留言"
          filterKey="buyerMemo"
          width={110}
          data={BUYER_MEMO_LIST}
          value={filterOptions.buyerMemo}
          filterOptions={filterOptions}
          onFilterChange={onFilterChange}
          onFilterConfirm={onFilterConfirm}
        />
        <SubFilterSelect
          placeholder="是否加星"
          filterKey="star"
          width={100}
          data={STAR_LIST}
          value={filterOptions.star}
          filterOptions={filterOptions}
          onFilterChange={onFilterChange}
          onFilterConfirm={onFilterConfirm}
        />
        <SubFilterSelect
          placeholder="下单时间排序"
          filterKey="orderby"
          width={160}
          data={TIME_SORT_OPTIONS}
          value={`${filterOptions.orderby}/${filterOptions.order}`}
          filterOptions={filterOptions}
          onFilterChange={onFilterChange}
          onFilterConfirm={onFilterConfirm}
        />
      </div>
    </div>
  );
});

export default SubFilter;
