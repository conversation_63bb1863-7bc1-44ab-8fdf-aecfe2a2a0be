import React, { useEffect, useState, useMemo, useCallback } from 'react';
import { BlockLoading, BlockHeader, Icon } from 'zent';
import { DynamicTableTrigger, useDynamicTable } from '@youzan/dynamic-table';
import { useCloudHook } from '@youzan/ranta-cloud-react';
import { OrderManageListBeforeTableRender } from '@youzan-cloud/cloud-biz-types';

import ListItem from '../list-item';
import ListHeader from '../list-header';

// 创建 memoized ListItem 组件以避免不必要的重渲染
const MemoizedListItem = React.memo(ListItem, (prevProps, nextProps) => {
  // 自定义比较函数，只在关键属性变化时重新渲染
  return (
    prevProps.selected === nextProps.selected &&
    prevProps.data.orderNo === nextProps.data.orderNo &&
    prevProps.data.state === nextProps.data.state &&
    prevProps.data.feedback === nextProps.data.feedback &&
    JSON.stringify(prevProps.mergedColumns) === JSON.stringify(nextProps.mergedColumns) &&
    prevProps.loading === nextProps.loading
  );
});
import './index.scss';
import { IFormattedOrderListItem, IOrderSearchQuery } from '../../types';
import { IGetTipsResponse } from 'definitions/order/list';
import { ENABLE_DYNAMIC_TABLE } from '../../constants';
import { columns, IColumnOptions } from '../../columns';

interface IProps {
  list: IFormattedOrderListItem[];
  selectedItems: IFormattedOrderListItem[];
  loading: boolean;
  filterOptions: IOrderSearchQuery;
  current?: number;
  onFilterChange: (name: string, value: any) => unknown;
  onFilterConfirm: (filters: IOrderSearchQuery) => unknown;
  expressType: string;
  tips: IGetTipsResponse;
}

const LIST_CONFIG_KEY = 'trade-order-columns';

const OrderList: React.FC<IProps> = (props) => {
  const { list = [], loading = false, filterOptions, selectedItems } = props;
  const dynamicTable = useDynamicTable(ENABLE_DYNAMIC_TABLE ? LIST_CONFIG_KEY : '', columns);
  const [mergedColumns, setMergedColumns] = useState<IColumnOptions[]>([]);

  /**
   * beforeTableRender
   * @desc 订单列表渲染前触发。可以用来修改/增加/减少列
   */
  const invokeBeforeTableRender = useCloudHook<OrderManageListBeforeTableRender>(
    'beforeTableRender',
    {
      allowMultiple: true,
    }
  );

  // 使用 useMemo 缓存选中项的映射，避免每次渲染都重新计算
  const selectedItemsMap = useMemo(() => {
    const map = new Map();
    selectedItems.forEach(item => {
      map.set(item.orderNo, true);
    });
    return map;
  }, [selectedItems]);

  // 使用 useCallback 缓存 fetchColumns 函数
  const fetchColumns = useCallback(async () => {
    try {
      const { selectedColumns } = dynamicTable;
      const sortableColumns = selectedColumns.filter(i => i.sortable);
      const cloudColumn = sortableColumns.map((item) => {
        const { title, key: name, width } = item;
        return { title: title as string, name, width };
      });

      const configs = await invokeBeforeTableRender({ columns: cloudColumn });
      let res: IColumnOptions[] = [];

      if (configs.length > 0) {
        configs.forEach((config) => {
          const { columns: returnColumns = [] } = config || {};

          if (!Array.isArray(returnColumns) || returnColumns.length === 0) return;

          returnColumns.forEach((column) => {
            const col = sortableColumns.find((v) => v.key === column.name);
            if (!col) {
              const { title, name, width, hidden } = column;
              res.push({
                key: `cloud_${name}`,
                width,
                hidden,
                title: <th className="list-header-item state-cell">{title}</th>,
              });
              return;
            }
            if (res.find((v) => v.key === col.key)) return;
            res.push(col);
          });
        });
      } else {
        res = sortableColumns;
      }

      // 使用深度比较避免不必要的状态更新
      setMergedColumns(prevColumns => {
        if (JSON.stringify(res) !== JSON.stringify(prevColumns)) {
          return res;
        }
        return prevColumns;
      });
    } catch (err) {
      console.log(err);
    }
  }, [dynamicTable.selectedColumns, invokeBeforeTableRender]);

  useEffect(() => {
    fetchColumns();
  }, [fetchColumns]);

  // 使用 useCallback 缓存滚动处理函数
  const handleScroll = useCallback((e: Event) => {
    const header = document.querySelectorAll('.order-list .list-header');
    const items = document.querySelectorAll('.order-list .list-item-body');
    const elms = [...Array.from(header), ...Array.from(items)];

    const { scrollLeft = 0, clientWidth = 0, scrollWidth = 0 } = e?.target as Element || {};

    elms.forEach((elm) => {
      (elm as HTMLElement).scrollLeft = scrollLeft;

      if (scrollLeft > 0) {
        elm.classList?.add('fixed-left');
      } else {
        elm.classList?.remove('fixed-left');
      }
      if (scrollLeft + clientWidth < scrollWidth) {
        elm.classList?.add('fixed-right');
      } else {
        elm.classList?.remove('fixed-right');
      }
    });
  }, []);

  // 支持列头与列项滚动联动
  useEffect(() => {
    if (!list.length) return;

    let dispose: Function;
    const timeoutId = setTimeout(() => {
      const header = document.querySelectorAll('.order-list .list-header');
      const items = document.querySelectorAll('.order-list .list-item-body');
      const elms = [...Array.from(header), ...Array.from(items)];

      elms.forEach((item) => {
        (item as HTMLElement).scrollLeft = (header[0] as HTMLElement)?.scrollLeft || 0;
        item.addEventListener('scroll', handleScroll);
      });

      dispose = () => elms.forEach((item) => item.removeEventListener('scroll', handleScroll));
    });

    return () => {
      clearTimeout(timeoutId);
      dispose?.();
    };
  }, [list.length, handleScroll]); // 只依赖 list.length，避免每次 list 变化都重新绑定事件

  return (
    <div className="order-list">
      {filterOptions.tuanId && (
        <BlockHeader
          className="order-list-block-header"
          title={`同团订单列表 团编号: ${filterOptions.tuanId}`}
          rightContent={
            <a href="#" onClick={() => window.history.back()}>
              返回全部订单
            </a>
          }
        />
      )}
      {ENABLE_DYNAMIC_TABLE && (
        <DynamicTableTrigger
          className="order-list-dynamic-table-trigger"
          columns={dynamicTable.columns}
          defaultValue={dynamicTable.selectedItems}
          onConfirm={dynamicTable.setSelectedItems}
        >
          <Icon type="settings-o" />
          <span>设置字段</span>
        </DynamicTableTrigger>
      )}
      <ListHeader className="list-header-without-scrollbar" mergedColumns={mergedColumns} />
      <BlockLoading loading={loading}>
        {list && list.length ? (
          <div className="order-list-body">
            {list.map((item) => {
              const isSelected = selectedItemsMap.has(item.orderNo);
              return (
                <MemoizedListItem
                  selected={isSelected}
                  data={item}
                  key={item.orderNo}
                  dynamicTable={dynamicTable}
                  mergedColumns={mergedColumns}
                  {...props}
                />
              );
            })}
          </div>
        ) : (
          <div className="table-empty-content">
            <i className="table-empty-content__query-icon" />
            <span>暂无订单</span>
          </div>
        )}
      </BlockLoading>
      <ListHeader className="list-header-with-scrollbar" mergedColumns={mergedColumns} />
    </div>
  );
};

export default OrderList;
